{"metadata": {"report_id": "pentest_20250728_175301", "generated_at": "2025-07-28T17:53:01.212800", "tool_version": "1.0.0", "scan_duration": 0.33093738555908203}, "target_info": {"url": "https://console.qmai.co/login", "tech_stack": "Unknown", "app_type": "Web Application", "endpoints": ["https://console.qmai.co/api/v1", "https://console.qmai.co/api", "https://console.qmai.co/login", "https://console.qmai.co/dashboard", "https://console.qmai.co/download", "https://console.qmai.co/config", "https://console.qmai.co/.env", "https://console.qmai.co/admin", "https://console.qmai.co/profile", "https://console.qmai.co/user"], "special_features": ["File Upload"]}, "summary": {"total_scenarios": 1, "successful_scenarios": 1, "total_vulnerabilities": 1, "severity_breakdown": {"critical": 0, "high": 1, "medium": 0, "low": 0}, "success_rate": 100.0}, "vulnerabilities": [{"scenario": "基础SQL注入测试", "target": "https://console.qmai.co/login/", "type": "xss", "severity": "high", "confidence": "high", "evidence": "<!doctype html><html lang=\"zh-CN\" id=\"console-root\"><head><meta charset=\"UTF-8\"/><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"/><meta name=\"renderer\" content=\"webkit\"/><meta name=\"goog...", "indicator": "<script>"}], "scan_results": {"target_url": "https://console.qmai.co/login", "start_time": 1753696380.8775053, "results": [{"scenario_id": "basic_sql_injection", "scenario_name": "基础SQL注入测试", "target_url": "https://console.qmai.co/login/", "success": true, "vulnerabilities": [{"type": "xss", "indicator": "<script>", "confidence": "high", "evidence": "<!doctype html><html lang=\"zh-CN\" id=\"console-root\"><head><meta charset=\"UTF-8\"/><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"/><meta name=\"renderer\" content=\"webkit\"/><meta name=\"google\" content=\"notranslate\"/><meta name=\"viewport\" content=\"width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1\"/><title>企迈</title><link rel=\"preconnect\" href=\"https://rscdn.qmai.cn/\" crossorigin/><link rel=\"dns-prefetch\" href=\"https://rscdn.qmai.cn/\"/><link rel=\"icon\" href=\"https://rsc", "severity": "high"}], "responses": [{"step": 1, "action": "GET request with SQL injection payload", "payload": "id=1' OR '1'='1", "response": {"url": "https://console.qmai.co/login/?id=1' OR '1'='1", "method": "GET", "status_code": 200, "headers": {"Server": "Tengin<PERSON>", "Content-Type": "text/html; charset=utf-8", "Transfer-Encoding": "chunked", "Connection": "keep-alive", "Date": "Mon, 28 Jul 2025 09:53:00 GMT", "Vary": "Accept-Encoding, Accept-Encoding", "Last-Modified": "Mon, 28 Jul 2025 06:50:58 GMT", "Cache-Control": "no-cache", "Content-Encoding": "gzip", "Via": "cache21.l2cn7828[81,81,200-0,M], cache9.l2cn7828[82,0], vcache10.cn1242[97,97,200-0,M], vcache12.cn1242[101,0]", "Ali-Swift-Global-Savetime": "1753696380", "X-Cache": "MISS TCP_MISS dirn:-2:-2", "X-Swift-SaveTime": "Mon, 28 Jul 2025 09:53:00 GMT", "X-Swift-CacheTime": "0", "Timing-Allow-Origin": "*", "EagleId": "3b3feea017536963806345653e"}, "content": "<!doctype html><html lang=\"zh-CN\" id=\"console-root\"><head><meta charset=\"UTF-8\"/><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"/><meta name=\"renderer\" content=\"webkit\"/><meta name=\"google\" content=\"notranslate\"/><meta name=\"viewport\" content=\"width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1\"/><title>企迈</title><link rel=\"preconnect\" href=\"https://rscdn.qmai.cn/\" crossorigin/><link rel=\"dns-prefetch\" href=\"https://rscdn.qmai.cn/\"/><link rel=\"icon\" href=\"https://rscdn.qmai.cn/console-vue/favicon.ico?v=2.0.0\"/><link rel=\"stylesheet\" href=\"https://rscdn.qmai.cn/npm-packages/console-entry/1.0.0/index.css\"/><link rel=\"stylesheet\" href=\"https://rscdn.qmai.cn/npm-packages/normalize.css/8.0.1/normalize.css\"/><link rel=\"stylesheet\" href=\"https://rscdn.qmai.cn/npm-packages/qmai-ui/v0.30.0/theme-chalk/index.css\"/><link rel=\"stylesheet\" href=\"https://unpkg.qmai.cn/@qmai/qmai-ui-next@0.1.0/lib/theme-chalk/index.css\"/><link rel=\"stylesheet\" href=\"https://unpkg.qmai.cn/@qmai/iconfont-sealicon@latest\"/><script src=\"https://rscdn.qmai.cn/npm-packages/console-entry/1.0.0/index.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/vue/2.7.15/dist/vue.min.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/vue-router/3.5.1/vue-router.min.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/vuex/3.6.2/vuex.min.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/echarts/4.9.0/echarts.min.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/qmai-ui/v0.30.0/index.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/axios/0.26.1/axios.min.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/lodash/4.17.21/lodash.min.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/xlsx/0.17.5/xlsx.full.min.js\"></script><script src=\"https://unpkg.qmai.cn/@qmai/vue-kylin-deps@beta\"></script><script src=\"https://unpkg.qmai.cn/@qmai/vue-kylin@beta\"></script><script src=\"https://unpkg.qmai.cn/@qmai/qmai-ui-next@latest\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/aliyun/es6-promise.min.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/aliyun/aliyun-oss-sdk-5.3.1.min.js\"></script><script src=\"https://rscdn.qmai.cn/npm-packages/aliyun/aliyun-upload-sdk-1.5.0.min.js\"></script><script src=\"https://map.qq.com/api/js?v=2.exp&key=JFABZ-F5BC6-QEOSQ-M2CPA-A55FK-KUFEG\"></script><script src=\"https://o.alicdn.com/captcha-frontend/aliyunCaptcha/AliyunCaptcha.js\"></script><script defer=\"defer\" src=\"https://rscdn.qmai.co/console/bac2f87/js/chunk-vendors.4972dcbd.js\"></script><script defer=\"defer\" src=\"https://rscdn.qmai.co/console/bac2f87/js/index.d7aeb4f5.js\"></script><script>console.log(\n      '%c ' + \"console-vue bac2f87e beta\" + ' %c',\n      'color: #ffffff; background: #137dee; padding:5px 0;',\n      'background: #fadfa3; padding:5px 0;'\n    )</script></head><body><div id=\"console-vue--loading\" class=\"global-console-vue--loading\"><img src=\"https://images.qmai.cn/resource/20211217174910/2023/11/17/loading.gif\" width=\"200\" height=\"100\" draggable=\"false\"/></div><div id=\"console-vue\"></div></body></html>", "response_time": 119.08197402954102, "content_length": 3125}, "vulnerability_detected": true, "vulnerability_info": {"type": "xss", "indicator": "<script>", "confidence": "high", "evidence": "<!doctype html><html lang=\"zh-CN\" id=\"console-root\"><head><meta charset=\"UTF-8\"/><meta http-equiv=\"X-UA-Compatible\" content=\"IE=edge,chrome=1\"/><meta name=\"renderer\" content=\"webkit\"/><meta name=\"google\" content=\"notranslate\"/><meta name=\"viewport\" content=\"width=device-width,initial-scale=1,maximum-scale=1,minimum-scale=1\"/><title>企迈</title><link rel=\"preconnect\" href=\"https://rscdn.qmai.cn/\" crossorigin/><link rel=\"dns-prefetch\" href=\"https://rscdn.qmai.cn/\"/><link rel=\"icon\" href=\"https://rsc", "severity": "high"}}], "execution_time": 0.11908197402954102, "error": null}], "statistics": {"total_requests": 1, "successful_requests": 1, "failed_requests": 0, "vulnerabilities_found": 1, "success_rate": 100.0, "scan_duration": 0.33093738555908203, "requests_per_second": 1.0}, "end_time": 1753696381.2084427, "scan_duration": 0.33093738555908203}, "statistics": {"total_requests": 1, "successful_requests": 1, "failed_requests": 0, "vulnerabilities_found": 1, "success_rate": 100.0, "scan_duration": 0.33093738555908203, "requests_per_second": 1.0}, "recommendations": [{"title": "XSS防护", "priority": "high", "description": "实施输出编码和内容安全策略来防止XSS攻击", "actions": ["对所有用户输入进行HTML编码", "实施内容安全策略(CSP)", "使用安全的JavaScript框架", "验证和过滤用户输入"]}, {"title": "通用安全加固", "priority": "medium", "description": "实施基础的Web应用安全措施", "actions": ["启用HTTPS并配置安全的TLS设置", "实施适当的会话管理", "配置安全HTTP头部", "定期更新依赖组件", "实施访问日志和监控"]}]}