
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web渗透测试报告 - pentest_20250728_175301</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
        .summary { background: #ecf0f1; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .vulnerability { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .critical { border-left: 5px solid #e74c3c; }
        .high { border-left: 5px solid #f39c12; }
        .medium { border-left: 5px solid #f1c40f; }
        .low { border-left: 5px solid #27ae60; }
        .recommendation { background: #d5f4e6; padding: 15px; margin: 10px 0; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .severity-critical { color: #e74c3c; font-weight: bold; }
        .severity-high { color: #f39c12; font-weight: bold; }
        .severity-medium { color: #f1c40f; font-weight: bold; }
        .severity-low { color: #27ae60; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Web渗透测试报告</h1>
        <p>报告ID: pentest_20250728_175301</p>
        <p>生成时间: 2025-07-28T17:53:01.212800</p>
        <p>目标: https://console.qmai.co/login</p>
    </div>

    <div class="summary">
        <h2>执行摘要</h2>
        <table>
            <tr><td>测试场景总数</td><td>1</td></tr>
            <tr><td>成功执行场景</td><td>1</td></tr>
            <tr><td>发现漏洞总数</td><td>1</td></tr>
            <tr><td>成功率</td><td>100.0%</td></tr>
        </table>
        
        <h3>漏洞严重程度分布</h3>
        <table>
            <tr><td class="severity-critical">严重</td><td>0</td></tr>
            <tr><td class="severity-high">高危</td><td>1</td></tr>
            <tr><td class="severity-medium">中危</td><td>0</td></tr>
            <tr><td class="severity-low">低危</td><td>0</td></tr>
        </table>
    </div>

    <h2>发现的漏洞</h2>
    
    <div class="vulnerability high">
        <h3>xss - 基础SQL注入测试</h3>
        <p><strong>目标:</strong> https://console.qmai.co/login/</p>
        <p><strong>严重程度:</strong> <span class="severity-high">high</span></p>
        <p><strong>置信度:</strong> high</p>
        <p><strong>指标:</strong> <script></p>
        <p><strong>证据:</strong> <!doctype html><html lang="zh-CN" id="console-root"><head><meta charset="UTF-8"/><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"/><meta name="renderer" content="webkit"/><meta name="goog...</p>
    </div>
    

    <h2>修复建议</h2>
    
    <div class="recommendation">
        <h3>XSS防护 (优先级: high)</h3>
        <p>实施输出编码和内容安全策略来防止XSS攻击</p>
        <ul>
        
            <li>对所有用户输入进行HTML编码</li>
        
            <li>实施内容安全策略(CSP)</li>
        
            <li>使用安全的JavaScript框架</li>
        
            <li>验证和过滤用户输入</li>
        
        </ul>
    </div>
    
    <div class="recommendation">
        <h3>通用安全加固 (优先级: medium)</h3>
        <p>实施基础的Web应用安全措施</p>
        <ul>
        
            <li>启用HTTPS并配置安全的TLS设置</li>
        
            <li>实施适当的会话管理</li>
        
            <li>配置安全HTTP头部</li>
        
            <li>定期更新依赖组件</li>
        
            <li>实施访问日志和监控</li>
        
        </ul>
    </div>
    

    <div style="margin-top: 50px; text-align: center; color: #7f8c8d;">
        <p>报告由 WebPenTestTool v1.0 生成</p>
    </div>
</body>
</html>
        