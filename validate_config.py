#!/usr/bin/env python3
"""
配置验证脚本
检查工具配置是否正确
"""

import os
import sys
import yaml
from pathlib import Path
from dotenv import load_dotenv


def validate_config_file():
    """验证配置文件"""
    print("🔍 验证配置文件...")
    
    config_path = Path("config/config.yaml")
    if not config_path.exists():
        print("❌ 配置文件不存在: config/config.yaml")
        return False
    
    try:
        with open(config_path, 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
    except yaml.YAMLError as e:
        print(f"❌ 配置文件格式错误: {e}")
        return False
    
    # 检查必需的配置节
    required_sections = {
        'model': ['name', 'api_endpoint', 'max_tokens', 'temperature', 'timeout'],
        'logging': ['level', 'format', 'file_max_size', 'backup_count', 'console_output'],
        'scanning': ['max_concurrent_requests', 'request_timeout', 'user_agent'],
        'reporting': ['format', 'export_path'],
        'security': ['rate_limiting', 'requests_per_second']
    }
    
    for section, keys in required_sections.items():
        if section not in config:
            print(f"❌ 缺少配置节: {section}")
            return False
        
        for key in keys:
            if key not in config[section]:
                print(f"❌ 缺少配置项: {section}.{key}")
                return False
    
    print("✅ 配置文件验证通过")
    return True


def validate_environment():
    """验证环境变量"""
    print("🔍 验证环境变量...")
    
    # 加载环境变量
    load_dotenv()
    
    # 检查API密钥
    api_key = os.getenv('QWQ_API_KEY')
    if not api_key:
        print("⚠️  未设置 QWQ_API_KEY 环境变量")
        print("   请在 .env 文件中设置您的API密钥")
        return False
    
    if len(api_key) < 10:
        print("⚠️  API密钥长度可能不正确")
        return False
    
    # 检查API端点
    api_endpoint = os.getenv('QWQ_API_ENDPOINT')
    if api_endpoint and not api_endpoint.startswith(('http://', 'https://')):
        print("❌ API端点格式错误")
        return False
    
    print("✅ 环境变量验证通过")
    return True


def validate_directories():
    """验证目录结构"""
    print("🔍 验证目录结构...")
    
    required_dirs = [
        "modules",
        "prompts", 
        "config",
        "logs",
        "reports"
    ]
    
    for dir_name in required_dirs:
        dir_path = Path(dir_name)
        if not dir_path.exists():
            print(f"⚠️  目录不存在: {dir_name}")
            try:
                dir_path.mkdir(exist_ok=True)
                print(f"✅ 已创建目录: {dir_name}")
            except Exception as e:
                print(f"❌ 无法创建目录 {dir_name}: {e}")
                return False
    
    print("✅ 目录结构验证通过")
    return True


def validate_dependencies():
    """验证依赖包"""
    print("🔍 验证依赖包...")
    
    required_packages = [
        'requests',
        'pyyaml', 
        'rich',
        'colorama',
        'jinja2',
        'beautifulsoup4',
        'aiohttp',
        'python-dotenv'
    ]
    
    missing_packages = []
    
    for package in required_packages:
        try:
            __import__(package.replace('-', '_'))
        except ImportError:
            missing_packages.append(package)
    
    if missing_packages:
        print("❌ 缺少以下依赖包:")
        for package in missing_packages:
            print(f"   - {package}")
        print("\n请运行: pip install -r requirements.txt")
        return False
    
    print("✅ 依赖包验证通过")
    return True


def validate_modules():
    """验证自定义模块"""
    print("🔍 验证自定义模块...")
    
    modules_to_test = [
        'modules.logger',
        'modules.qwq_client',
        'modules.web_scanner', 
        'modules.report_generator',
        'prompts.pentest_prompts'
    ]
    
    for module_name in modules_to_test:
        try:
            __import__(module_name)
            print(f"✅ {module_name}")
        except ImportError as e:
            print(f"❌ {module_name}: {e}")
            return False
    
    print("✅ 自定义模块验证通过")
    return True


def validate_permissions():
    """验证文件权限"""
    print("🔍 验证文件权限...")
    
    # 检查写入权限
    test_dirs = ["logs", "reports"]
    
    for dir_name in test_dirs:
        dir_path = Path(dir_name)
        test_file = dir_path / "test_write.tmp"
        
        try:
            with open(test_file, 'w') as f:
                f.write("test")
            test_file.unlink()  # 删除测试文件
            print(f"✅ {dir_name} 目录写入权限正常")
        except Exception as e:
            print(f"❌ {dir_name} 目录写入权限错误: {e}")
            return False
    
    print("✅ 文件权限验证通过")
    return True


def generate_validation_report():
    """生成验证报告"""
    print("\n" + "="*60)
    print("配置验证报告")
    print("="*60)
    
    validations = [
        ("配置文件", validate_config_file),
        ("环境变量", validate_environment),
        ("目录结构", validate_directories),
        ("依赖包", validate_dependencies),
        ("自定义模块", validate_modules),
        ("文件权限", validate_permissions)
    ]
    
    results = {}
    all_passed = True
    
    for name, validator in validations:
        print(f"\n{name}:")
        print("-" * 20)
        result = validator()
        results[name] = result
        if not result:
            all_passed = False
    
    print("\n" + "="*60)
    print("验证结果摘要")
    print("="*60)
    
    for name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:15} {status}")
    
    if all_passed:
        print("\n🎉 所有验证通过！工具已准备就绪。")
        print("\n下一步:")
        print("1. 运行: python main.py --test-api")
        print("2. 运行: python main.py https://httpbin.org")
    else:
        print("\n⚠️  部分验证失败，请根据上述信息修复问题。")
    
    return all_passed


def main():
    """主函数"""
    print("Web渗透测试工具 - 配置验证")
    print("="*60)
    
    if len(sys.argv) > 1 and sys.argv[1] == "--quick":
        # 快速验证模式
        config_ok = validate_config_file()
        env_ok = validate_environment()
        deps_ok = validate_dependencies()
        
        if config_ok and env_ok and deps_ok:
            print("✅ 快速验证通过")
            return 0
        else:
            print("❌ 快速验证失败")
            return 1
    else:
        # 完整验证模式
        success = generate_validation_report()
        return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
