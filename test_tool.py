#!/usr/bin/env python3
"""
Web渗透测试工具测试脚本
用于验证工具的各个组件功能
"""

import json
import sys
import time
from pathlib import Path
import yaml

# 导入工具模块
from modules.logger import get_logger
from modules.qwq_client import QwQClient
from modules.web_scanner import WebScanner
from modules.report_generator import ReportGenerator
from prompts.pentest_prompts import get_prompt_generator

logger = get_logger()


class ToolTester:
    """工具测试类"""
    
    def __init__(self):
        self.config = self._load_config()
        self.test_results = {
            'logger': False,
            'config': False,
            'prompt_generator': False,
            'qwq_client': False,
            'web_scanner': False,
            'report_generator': False
        }
    
    def _load_config(self):
        """加载配置"""
        try:
            with open("config/config.yaml", 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except Exception as e:
            print(f"❌ 配置加载失败: {e}")
            return {}
    
    def test_logger(self):
        """测试日志系统"""
        print("🧪 测试日志系统...")
        
        try:
            logger.info("测试信息日志")
            logger.warning("测试警告日志")
            logger.error("测试错误日志")
            logger.debug("测试调试日志")
            
            # 测试特殊日志方法
            logger.log_test_start("https://example.com", "测试场景")
            logger.log_test_result("https://example.com", "测试场景", "成功", "high")
            logger.log_vulnerability_found("SQL注入", "https://example.com", {"test": "data"})
            logger.log_scan_progress(1, 5, "https://example.com")
            
            self.test_results['logger'] = True
            print("✅ 日志系统测试通过")
            
        except Exception as e:
            print(f"❌ 日志系统测试失败: {e}")
    
    def test_config(self):
        """测试配置系统"""
        print("🧪 测试配置系统...")
        
        try:
            required_sections = ['model', 'logging', 'scanning', 'reporting', 'security']
            
            for section in required_sections:
                if section not in self.config:
                    raise ValueError(f"缺少配置节: {section}")
            
            # 检查关键配置项
            model_config = self.config['model']
            required_model_keys = ['name', 'api_endpoint', 'max_tokens', 'temperature']
            
            for key in required_model_keys:
                if key not in model_config:
                    raise ValueError(f"缺少模型配置项: {key}")
            
            self.test_results['config'] = True
            print("✅ 配置系统测试通过")
            
        except Exception as e:
            print(f"❌ 配置系统测试失败: {e}")
    
    def test_prompt_generator(self):
        """测试提示词生成器"""
        print("🧪 测试提示词生成器...")
        
        try:
            prompt_gen = get_prompt_generator()
            
            # 测试场景生成提示词
            target_info = {
                'url': 'https://example.com',
                'tech_stack': 'Apache + PHP',
                'app_type': 'Web Application',
                'endpoints': ['/login', '/admin', '/api'],
                'special_features': ['Authentication', 'File Upload']
            }
            
            scenario_prompt = prompt_gen.generate_scenario_prompt(target_info)
            if not scenario_prompt or len(scenario_prompt) < 100:
                raise ValueError("场景提示词生成失败")
            
            # 测试载荷生成提示词
            payload_prompt = prompt_gen.generate_payload_prompt(
                'sql_injection',
                {'url': 'https://example.com', 'parameter': 'id'}
            )
            if not payload_prompt or len(payload_prompt) < 100:
                raise ValueError("载荷提示词生成失败")
            
            # 测试分析提示词
            response_data = {
                'status_code': 500,
                'headers': {'Server': 'Apache'},
                'content_length': 1024,
                'response_time': 200,
                'errors': ['MySQL error']
            }
            
            analysis_prompt = prompt_gen.generate_analysis_prompt(response_data)
            if not analysis_prompt or len(analysis_prompt) < 100:
                raise ValueError("分析提示词生成失败")
            
            # 测试OWASP Top 10提示词
            owasp_prompts = prompt_gen.get_owasp_top10_prompts()
            if not owasp_prompts or len(owasp_prompts) < 5:
                raise ValueError("OWASP提示词获取失败")
            
            self.test_results['prompt_generator'] = True
            print("✅ 提示词生成器测试通过")
            
        except Exception as e:
            print(f"❌ 提示词生成器测试失败: {e}")
    
    def test_qwq_client(self):
        """测试QwQ客户端"""
        print("🧪 测试QwQ客户端...")
        
        try:
            # 检查是否有API密钥
            import os
            from dotenv import load_dotenv
            load_dotenv()
            
            api_key = os.getenv('QWQ_API_KEY')
            if not api_key:
                print("⚠️  未设置API密钥，跳过QwQ客户端测试")
                return
            
            client = QwQClient(self.config)
            
            # 测试模型信息获取
            model_info = client.get_model_info()
            if not model_info or not model_info.get('api_key_configured'):
                raise ValueError("模型信息获取失败")
            
            # 测试连接（如果有API密钥）
            if client.test_connection():
                print("✅ API连接测试成功")
            else:
                print("⚠️  API连接测试失败，但客户端初始化正常")
            
            self.test_results['qwq_client'] = True
            print("✅ QwQ客户端测试通过")
            
        except Exception as e:
            print(f"❌ QwQ客户端测试失败: {e}")
    
    def test_web_scanner(self):
        """测试Web扫描器"""
        print("🧪 测试Web扫描器...")
        
        try:
            scanner = WebScanner(self.config)
            
            # 测试会话设置
            if not scanner.session:
                raise ValueError("HTTP会话初始化失败")
            
            # 测试端点验证
            valid_url = scanner._is_valid_endpoint("https://example.com/test", "https://example.com")
            if not valid_url:
                raise ValueError("端点验证逻辑错误")
            
            invalid_url = scanner._is_valid_endpoint("https://evil.com/test", "https://example.com")
            if invalid_url:
                raise ValueError("端点验证应该拒绝外部域名")
            
            # 测试统计信息
            stats = scanner.get_scan_statistics()
            if not isinstance(stats, dict):
                raise ValueError("统计信息格式错误")
            
            # 创建测试场景
            test_scenario = {
                'id': 'test_scenario',
                'name': '测试场景',
                'category': 'Test',
                'severity': 'low',
                'test_steps': [
                    {
                        'step': 1,
                        'action': 'GET request',
                        'payload': 'test=1',
                        'expected_response': 'Normal response'
                    }
                ]
            }
            
            # 注意：这里不实际执行网络请求，只测试结构
            print("✅ Web扫描器结构测试通过")
            
            self.test_results['web_scanner'] = True
            print("✅ Web扫描器测试通过")
            
        except Exception as e:
            print(f"❌ Web扫描器测试失败: {e}")
    
    def test_report_generator(self):
        """测试报告生成器"""
        print("🧪 测试报告生成器...")
        
        try:
            report_gen = ReportGenerator(self.config)
            
            # 创建测试数据
            test_scan_results = {
                'target_url': 'https://example.com',
                'start_time': time.time() - 100,
                'end_time': time.time(),
                'scan_duration': 100,
                'results': [
                    {
                        'scenario_id': 'test_1',
                        'scenario_name': '测试场景1',
                        'target_url': 'https://example.com',
                        'success': True,
                        'vulnerabilities': [
                            {
                                'type': 'sql_injection',
                                'severity': 'high',
                                'confidence': 'high',
                                'evidence': '测试证据',
                                'indicator': 'mysql_error'
                            }
                        ]
                    }
                ],
                'statistics': {
                    'total_requests': 10,
                    'successful_requests': 8,
                    'vulnerabilities_found': 1,
                    'success_rate': 80.0
                }
            }
            
            test_target_info = {
                'url': 'https://example.com',
                'tech_stack': 'Apache + PHP',
                'app_type': 'Web Application'
            }
            
            # 测试报告数据准备
            report_data = report_gen._prepare_report_data(test_scan_results, test_target_info)
            
            if not report_data or 'metadata' not in report_data:
                raise ValueError("报告数据准备失败")
            
            # 测试建议生成
            vulnerabilities = [{'type': 'sql_injection'}, {'type': 'xss'}]
            recommendations = report_gen._generate_recommendations(vulnerabilities)
            
            if not recommendations or len(recommendations) < 2:
                raise ValueError("建议生成失败")
            
            print("✅ 报告生成器结构测试通过")
            
            self.test_results['report_generator'] = True
            print("✅ 报告生成器测试通过")
            
        except Exception as e:
            print(f"❌ 报告生成器测试失败: {e}")
    
    def run_all_tests(self):
        """运行所有测试"""
        print("🚀 开始运行工具测试套件")
        print("="*50)
        
        self.test_logger()
        self.test_config()
        self.test_prompt_generator()
        self.test_qwq_client()
        self.test_web_scanner()
        self.test_report_generator()
        
        print("\n" + "="*50)
        print("📊 测试结果摘要")
        print("="*50)
        
        passed = sum(self.test_results.values())
        total = len(self.test_results)
        
        for component, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"{component:20} {status}")
        
        print(f"\n总计: {passed}/{total} 个组件测试通过")
        
        if passed == total:
            print("🎉 所有测试通过！工具已准备就绪。")
            return True
        else:
            print("⚠️  部分测试失败，请检查相关组件。")
            return False


def main():
    """主函数"""
    print("Web渗透测试工具 - 组件测试")
    print("="*50)
    
    tester = ToolTester()
    success = tester.run_all_tests()
    
    if success:
        print("\n下一步:")
        print("1. 设置API密钥: 编辑 .env 文件")
        print("2. 测试API连接: python main.py --test-api")
        print("3. 运行完整测试: python main.py https://httpbin.org")
    
    return 0 if success else 1


if __name__ == "__main__":
    sys.exit(main())
