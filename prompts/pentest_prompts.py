"""
渗透测试提示词工程模块
包含专门设计的QwQ-32B模型提示词模板
"""

from typing import Dict, List, Optional
from jinja2 import Template


class PenTestPrompts:
    """渗透测试提示词生成器"""
    
    def __init__(self):
        self.base_system_prompt = """你是一名资深的Web安全专家和渗透测试工程师，具有15年以上的实战经验。
你的任务是分析给定的Web应用目标，生成具体的、可执行的渗透测试场景。

核心要求：
1. 基于OWASP Top 10和最新的Web安全威胁
2. 生成的测试场景必须具体、可执行
3. 包含详细的测试步骤和预期结果
4. 考虑目标的技术栈和架构特点
5. 遵循负责任的安全测试原则

输出格式要求：
- 使用JSON格式返回
- 每个场景包含：名称、描述、严重程度、测试步骤、预期结果、缓解建议
- 严重程度分为：low, medium, high, critical
"""

    def generate_scenario_prompt(self, target_info: Dict) -> str:
        """生成渗透测试场景的提示词"""
        
        template = Template("""
{{ system_prompt }}

目标信息：
- URL: {{ target_url }}
- 技术栈: {{ tech_stack }}
- 应用类型: {{ app_type }}
- 已知端点: {{ endpoints }}
- 特殊功能: {{ special_features }}

请为此目标生成5个不同类型的渗透测试场景，覆盖以下安全领域：
1. 注入攻击（SQL注入、XSS、命令注入等）
2. 身份验证和会话管理
3. 访问控制和权限提升
4. 敏感数据暴露
5. 安全配置错误

每个场景的JSON格式如下：
{
  "scenarios": [
    {
      "id": "场景唯一标识",
      "name": "场景名称",
      "category": "OWASP分类",
      "severity": "严重程度(low/medium/high/critical)",
      "description": "详细描述",
      "target_endpoints": ["相关端点列表"],
      "test_steps": [
        {
          "step": 1,
          "action": "具体操作",
          "payload": "测试载荷",
          "expected_response": "预期响应"
        }
      ],
      "success_indicators": ["成功指标列表"],
      "risk_assessment": "风险评估",
      "mitigation": "缓解建议"
    }
  ]
}

请确保生成的场景：
- 针对性强，适合当前目标
- 测试步骤清晰可执行
- 包含具体的测试载荷
- 考虑实际的安全影响
""")
        
        return template.render(
            system_prompt=self.base_system_prompt,
            target_url=target_info.get('url', ''),
            tech_stack=target_info.get('tech_stack', 'Unknown'),
            app_type=target_info.get('app_type', 'Web Application'),
            endpoints=target_info.get('endpoints', []),
            special_features=target_info.get('special_features', [])
        )
    
    def generate_payload_prompt(self, vulnerability_type: str, context: Dict) -> str:
        """生成特定漏洞类型的测试载荷提示词"""
        
        template = Template("""
{{ system_prompt }}

任务：为特定漏洞类型生成测试载荷

漏洞类型: {{ vuln_type }}
上下文信息:
- 目标URL: {{ target_url }}
- 参数名称: {{ parameter_name }}
- 参数类型: {{ parameter_type }}
- 输入验证: {{ input_validation }}
- 技术栈: {{ tech_stack }}

请生成10个不同的测试载荷，包括：
1. 基础测试载荷
2. 绕过过滤器的载荷
3. 编码变形载荷
4. 时间盲注载荷（如适用）
5. 布尔盲注载荷（如适用）

输出JSON格式：
{
  "payloads": [
    {
      "id": "载荷ID",
      "payload": "具体载荷内容",
      "encoding": "编码方式",
      "description": "载荷说明",
      "expected_behavior": "预期行为",
      "detection_method": "检测方法"
    }
  ]
}
""")
        
        return template.render(
            system_prompt=self.base_system_prompt,
            vuln_type=vulnerability_type,
            target_url=context.get('url', ''),
            parameter_name=context.get('parameter', ''),
            parameter_type=context.get('param_type', ''),
            input_validation=context.get('validation', 'Unknown'),
            tech_stack=context.get('tech_stack', 'Unknown')
        )
    
    def generate_analysis_prompt(self, response_data: Dict) -> str:
        """生成响应分析的提示词"""
        
        template = Template("""
{{ system_prompt }}

任务：分析Web应用响应，识别潜在的安全漏洞

响应数据：
- HTTP状态码: {{ status_code }}
- 响应头: {{ headers }}
- 响应体长度: {{ content_length }}
- 响应时间: {{ response_time }}ms
- 错误信息: {{ error_messages }}
- 数据库错误: {{ db_errors }}

请分析以上响应数据，识别可能的安全问题：

输出JSON格式：
{
  "analysis": {
    "vulnerabilities_detected": [
      {
        "type": "漏洞类型",
        "confidence": "置信度(0-100)",
        "evidence": "证据描述",
        "severity": "严重程度",
        "recommendation": "修复建议"
      }
    ],
    "information_disclosure": [
      {
        "type": "信息泄露类型",
        "details": "具体内容",
        "risk_level": "风险等级"
      }
    ],
    "next_steps": ["建议的后续测试步骤"]
  }
}
""")
        
        return template.render(
            system_prompt=self.base_system_prompt,
            status_code=response_data.get('status_code', ''),
            headers=response_data.get('headers', {}),
            content_length=response_data.get('content_length', 0),
            response_time=response_data.get('response_time', 0),
            error_messages=response_data.get('errors', []),
            db_errors=response_data.get('db_errors', [])
        )
    
    def get_owasp_top10_prompts(self) -> Dict[str, str]:
        """获取OWASP Top 10相关的提示词"""
        return {
            "A01_Broken_Access_Control": """
            生成访问控制绕过测试场景，包括：
            - 垂直权限提升
            - 水平权限提升  
            - 直接对象引用
            - 强制浏览
            """,
            
            "A02_Cryptographic_Failures": """
            生成加密失败测试场景，包括：
            - 弱加密算法检测
            - 敏感数据传输安全
            - 密钥管理问题
            - 随机数生成缺陷
            """,
            
            "A03_Injection": """
            生成注入攻击测试场景，包括：
            - SQL注入（联合查询、布尔盲注、时间盲注）
            - NoSQL注入
            - 命令注入
            - LDAP注入
            - XPath注入
            """,
            
            "A04_Insecure_Design": """
            生成不安全设计测试场景，包括：
            - 业务逻辑缺陷
            - 工作流程绕过
            - 竞态条件
            - 状态机缺陷
            """,
            
            "A05_Security_Misconfiguration": """
            生成安全配置错误测试场景，包括：
            - 默认配置检测
            - 目录遍历
            - 敏感文件暴露
            - HTTP安全头缺失
            """
        }


# 全局提示词生成器实例
prompt_generator = PenTestPrompts()


def get_prompt_generator() -> PenTestPrompts:
    """获取提示词生成器实例"""
    return prompt_generator
