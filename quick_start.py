#!/usr/bin/env python3
"""
Web渗透测试工具快速启动脚本
提供交互式的工具使用体验
"""

import os
import sys
import time
from pathlib import Path
import subprocess

def print_banner():
    """打印工具横幅"""
    banner = """
╔══════════════════════════════════════════════════════════════╗
║                    Web渗透测试工具                           ║
║                基于QwQ-32B模型的智能安全测试                 ║
║                                                              ║
║  🚀 自动生成测试场景    🔍 智能漏洞检测                      ║
║  📊 多格式报告生成      🛡️ 安全限制保护                      ║
╚══════════════════════════════════════════════════════════════╝
    """
    print(banner)


def check_environment():
    """检查环境配置"""
    print("🔍 检查环境配置...")
    
    issues = []
    
    # 检查Python版本
    if sys.version_info < (3, 8):
        issues.append("Python版本需要3.8或更高")
    
    # 检查必要文件
    required_files = [
        "config/config.yaml",
        "requirements.txt",
        ".env.example"
    ]
    
    for file_path in required_files:
        if not Path(file_path).exists():
            issues.append(f"缺少文件: {file_path}")
    
    # 检查必要目录
    required_dirs = ["modules", "prompts"]
    for dir_path in required_dirs:
        if not Path(dir_path).exists():
            issues.append(f"缺少目录: {dir_path}")
    
    # 检查.env文件
    if not Path(".env").exists():
        issues.append("未找到.env文件，请复制.env.example并配置API密钥")
    
    if issues:
        print("❌ 发现以下问题:")
        for issue in issues:
            print(f"   - {issue}")
        return False
    else:
        print("✅ 环境检查通过")
        return True


def install_dependencies():
    """安装依赖"""
    print("📦 安装依赖包...")
    
    try:
        result = subprocess.run([
            sys.executable, "-m", "pip", "install", "-r", "requirements.txt"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 依赖安装完成")
            return True
        else:
            print(f"❌ 依赖安装失败: {result.stderr}")
            return False
    except Exception as e:
        print(f"❌ 依赖安装异常: {e}")
        return False


def setup_environment():
    """设置环境"""
    print("⚙️ 设置环境...")
    
    # 创建必要目录
    dirs = ["logs", "reports"]
    for dir_name in dirs:
        Path(dir_name).mkdir(exist_ok=True)
        print(f"📁 创建目录: {dir_name}")
    
    # 复制环境文件
    if not Path(".env").exists() and Path(".env.example").exists():
        import shutil
        shutil.copy(".env.example", ".env")
        print("📝 创建.env文件")
        print("⚠️  请编辑.env文件并设置您的API密钥")
    
    print("✅ 环境设置完成")


def test_installation():
    """测试安装"""
    print("🧪 测试安装...")
    
    try:
        result = subprocess.run([
            sys.executable, "test_tool.py"
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ 安装测试通过")
            return True
        else:
            print("❌ 安装测试失败")
            print("错误输出:")
            print(result.stderr)
            return False
    except Exception as e:
        print(f"❌ 测试异常: {e}")
        return False


def interactive_menu():
    """交互式菜单"""
    while True:
        print("\n" + "="*50)
        print("请选择操作:")
        print("1. 🔧 完整安装和设置")
        print("2. 🧪 运行组件测试")
        print("3. 📖 查看使用示例")
        print("4. 🚀 开始渗透测试")
        print("5. 🔗 测试API连接")
        print("6. 📚 查看帮助文档")
        print("0. 🚪 退出")
        print("="*50)
        
        choice = input("请输入选择 (0-6): ").strip()
        
        if choice == "0":
            print("👋 再见！")
            break
        elif choice == "1":
            full_setup()
        elif choice == "2":
            run_component_tests()
        elif choice == "3":
            show_examples()
        elif choice == "4":
            start_penetration_test()
        elif choice == "5":
            test_api_connection()
        elif choice == "6":
            show_help()
        else:
            print("❌ 无效选择，请重试")


def full_setup():
    """完整安装设置"""
    print("\n🔧 开始完整安装和设置...")
    
    if not check_environment():
        print("⚠️  环境检查失败，尝试修复...")
        setup_environment()
    
    if not install_dependencies():
        print("❌ 依赖安装失败，请手动安装")
        return
    
    setup_environment()
    
    if test_installation():
        print("🎉 安装设置完成！")
        print("\n下一步:")
        print("1. 编辑.env文件，设置API密钥")
        print("2. 运行API连接测试")
    else:
        print("❌ 安装测试失败，请检查错误信息")


def run_component_tests():
    """运行组件测试"""
    print("\n🧪 运行组件测试...")
    
    try:
        subprocess.run([sys.executable, "test_tool.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 组件测试失败")
    except FileNotFoundError:
        print("❌ 找不到测试文件")


def show_examples():
    """显示使用示例"""
    print("\n📖 运行使用示例...")
    
    try:
        subprocess.run([sys.executable, "examples/basic_usage.py"], check=True)
    except subprocess.CalledProcessError:
        print("❌ 示例运行失败")
    except FileNotFoundError:
        print("❌ 找不到示例文件")


def start_penetration_test():
    """开始渗透测试"""
    print("\n🚀 开始渗透测试...")
    
    target_url = input("请输入目标URL (例如: https://httpbin.org): ").strip()
    
    if not target_url:
        print("❌ 未输入目标URL")
        return
    
    if not target_url.startswith(('http://', 'https://')):
        print("❌ URL格式错误，必须以http://或https://开头")
        return
    
    print(f"🎯 开始测试目标: {target_url}")
    
    try:
        subprocess.run([sys.executable, "main.py", target_url], check=True)
    except subprocess.CalledProcessError:
        print("❌ 渗透测试失败")
    except FileNotFoundError:
        print("❌ 找不到主程序文件")


def test_api_connection():
    """测试API连接"""
    print("\n🔗 测试API连接...")
    
    try:
        subprocess.run([sys.executable, "main.py", "--test-api"], check=True)
    except subprocess.CalledProcessError:
        print("❌ API连接测试失败")
    except FileNotFoundError:
        print("❌ 找不到主程序文件")


def show_help():
    """显示帮助"""
    help_text = """
📚 Web渗透测试工具帮助文档

🔧 安装和配置:
1. 运行 python quick_start.py 进入交互模式
2. 选择"完整安装和设置"完成初始化
3. 编辑 .env 文件，设置 QWQ_API_KEY
4. 运行API连接测试确认配置正确

🚀 基本使用:
- 测试单个目标: python main.py https://example.com
- 测试API连接: python main.py --test-api
- 详细输出: python main.py https://example.com --verbose

📁 文件结构:
- main.py: 主程序入口
- config/config.yaml: 配置文件
- modules/: 核心功能模块
- prompts/: 提示词模板
- logs/: 日志文件
- reports/: 测试报告

🛡️ 安全注意事项:
- 仅对授权目标进行测试
- 遵守当地法律法规
- 使用适当的测试频率
- 保护测试结果的机密性

📞 获取支持:
- 查看 README.md 获取详细文档
- 运行 python test_tool.py 进行故障排除
- 检查 logs/ 目录中的日志文件
    """
    print(help_text)


def main():
    """主函数"""
    print_banner()
    
    # 快速环境检查
    if len(sys.argv) > 1 and sys.argv[1] == "--auto-setup":
        print("🔧 自动安装模式")
        full_setup()
        return
    
    # 检查基本环境
    if not Path("main.py").exists():
        print("❌ 未找到主程序文件，请确保在正确的目录中运行")
        return
    
    # 进入交互模式
    interactive_menu()


if __name__ == "__main__":
    main()
