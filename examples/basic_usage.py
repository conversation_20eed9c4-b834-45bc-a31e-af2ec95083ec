#!/usr/bin/env python3
"""
Web渗透测试工具基本使用示例
演示如何使用工具的各个组件
"""

import sys
import os
import json
from pathlib import Path

# 添加父目录到路径，以便导入模块
sys.path.append(str(Path(__file__).parent.parent))

from modules.logger import get_logger
from modules.qwq_client import QwQClient
from modules.web_scanner import WebScanner
from modules.report_generator import ReportGenerator
from prompts.pentest_prompts import get_prompt_generator
import yaml

logger = get_logger()


def load_config():
    """加载配置文件"""
    config_path = Path(__file__).parent.parent / "config" / "config.yaml"
    with open(config_path, 'r', encoding='utf-8') as f:
        return yaml.safe_load(f)


def example_1_basic_scenario_generation():
    """示例1: 基本场景生成"""
    print("\n" + "="*60)
    print("示例1: 基本场景生成")
    print("="*60)
    
    config = load_config()
    prompt_gen = get_prompt_generator()
    
    # 模拟目标信息
    target_info = {
        'url': 'https://demo.testfire.net',
        'tech_stack': 'Apache + Java',
        'app_type': 'Banking Web Application',
        'endpoints': ['/login', '/bank/main', '/bank/transfer', '/admin'],
        'special_features': ['Authentication', 'Money Transfer', 'Admin Panel']
    }
    
    # 生成场景提示词
    prompt = prompt_gen.generate_scenario_prompt(target_info)
    
    print("生成的提示词预览:")
    print("-" * 40)
    print(prompt[:500] + "...")
    
    print("\n✅ 场景生成提示词创建成功")


def example_2_mock_qwq_response():
    """示例2: 模拟QwQ响应处理"""
    print("\n" + "="*60)
    print("示例2: 模拟QwQ响应处理")
    print("="*60)
    
    # 模拟QwQ-32B的响应
    mock_response = {
        "scenarios": [
            {
                "id": "sql_injection_login",
                "name": "登录页面SQL注入测试",
                "category": "A03_Injection",
                "severity": "high",
                "description": "测试登录表单是否存在SQL注入漏洞",
                "target_endpoints": ["/login"],
                "test_steps": [
                    {
                        "step": 1,
                        "action": "POST request with SQL injection payload",
                        "payload": "username=admin' OR '1'='1&password=test",
                        "expected_response": "Successful login or database error"
                    },
                    {
                        "step": 2,
                        "action": "POST request with time-based blind injection",
                        "payload": "username=admin' AND SLEEP(5)--&password=test",
                        "expected_response": "Delayed response indicating vulnerability"
                    }
                ],
                "success_indicators": [
                    "mysql_fetch_array",
                    "syntax error",
                    "response time > 5 seconds"
                ],
                "risk_assessment": "High risk of unauthorized access and data breach",
                "mitigation": "Use parameterized queries and input validation"
            },
            {
                "id": "xss_search",
                "name": "搜索功能XSS测试",
                "category": "A03_Injection",
                "severity": "medium",
                "description": "测试搜索功能是否存在跨站脚本漏洞",
                "target_endpoints": ["/search"],
                "test_steps": [
                    {
                        "step": 1,
                        "action": "GET request with XSS payload",
                        "payload": "q=<script>alert('XSS')</script>",
                        "expected_response": "Script execution or reflection in response"
                    }
                ],
                "success_indicators": [
                    "<script>",
                    "alert(",
                    "javascript:"
                ],
                "risk_assessment": "Medium risk of session hijacking and data theft",
                "mitigation": "Implement output encoding and CSP headers"
            }
        ]
    }
    
    print("模拟的QwQ-32B响应:")
    print("-" * 40)
    print(json.dumps(mock_response, indent=2, ensure_ascii=False))
    
    print(f"\n✅ 成功解析 {len(mock_response['scenarios'])} 个测试场景")


def example_3_web_scanning_simulation():
    """示例3: Web扫描模拟"""
    print("\n" + "="*60)
    print("示例3: Web扫描模拟")
    print("="*60)
    
    config = load_config()
    scanner = WebScanner(config)
    
    # 使用示例2中的场景
    test_scenario = {
        "id": "sql_injection_login",
        "name": "登录页面SQL注入测试",
        "category": "A03_Injection",
        "severity": "high",
        "test_steps": [
            {
                "step": 1,
                "action": "GET request with SQL injection payload",
                "payload": "id=1' OR '1'='1",
                "expected_response": "Database error or unexpected behavior"
            }
        ]
    }
    
    # 模拟扫描结果（不实际发送请求）
    mock_result = {
        'scenario_id': test_scenario['id'],
        'scenario_name': test_scenario['name'],
        'target_url': 'https://demo.testfire.net/login',
        'success': True,
        'vulnerabilities': [
            {
                'type': 'sql_injection',
                'indicator': 'mysql_fetch_array',
                'confidence': 'high',
                'evidence': 'Warning: mysql_fetch_array() expects parameter...',
                'severity': 'high'
            }
        ],
        'responses': [
            {
                'step': 1,
                'action': 'GET request with SQL injection payload',
                'payload': "id=1' OR '1'='1",
                'response': {
                    'status_code': 500,
                    'content': 'Warning: mysql_fetch_array() expects parameter...',
                    'response_time': 250
                },
                'vulnerability_detected': True
            }
        ],
        'execution_time': 1.5
    }
    
    print("模拟扫描结果:")
    print("-" * 40)
    print(json.dumps(mock_result, indent=2, ensure_ascii=False))
    
    print(f"\n✅ 发现 {len(mock_result['vulnerabilities'])} 个漏洞")


def example_4_report_generation():
    """示例4: 报告生成"""
    print("\n" + "="*60)
    print("示例4: 报告生成")
    print("="*60)
    
    config = load_config()
    report_gen = ReportGenerator(config)
    
    # 模拟完整的扫描结果
    scan_results = {
        'target_url': 'https://demo.testfire.net',
        'start_time': 1640995200,  # 2022-01-01 00:00:00
        'end_time': 1640995320,    # 2022-01-01 00:02:00
        'scan_duration': 120,
        'results': [
            {
                'scenario_id': 'sql_injection_login',
                'scenario_name': '登录页面SQL注入测试',
                'target_url': 'https://demo.testfire.net/login',
                'success': True,
                'vulnerabilities': [
                    {
                        'type': 'sql_injection',
                        'severity': 'high',
                        'confidence': 'high',
                        'evidence': 'Warning: mysql_fetch_array() expects parameter...',
                        'indicator': 'mysql_fetch_array'
                    }
                ]
            },
            {
                'scenario_id': 'xss_search',
                'scenario_name': '搜索功能XSS测试',
                'target_url': 'https://demo.testfire.net/search',
                'success': True,
                'vulnerabilities': [
                    {
                        'type': 'xss',
                        'severity': 'medium',
                        'confidence': 'high',
                        'evidence': '<script>alert("XSS")</script>',
                        'indicator': '<script>'
                    }
                ]
            }
        ],
        'statistics': {
            'total_requests': 10,
            'successful_requests': 8,
            'vulnerabilities_found': 2,
            'success_rate': 80.0
        }
    }
    
    target_info = {
        'url': 'https://demo.testfire.net',
        'tech_stack': 'Apache + Java',
        'app_type': 'Banking Web Application'
    }
    
    # 准备报告数据
    report_data = report_gen._prepare_report_data(scan_results, target_info)
    
    print("报告数据摘要:")
    print("-" * 40)
    print(f"报告ID: {report_data['metadata']['report_id']}")
    print(f"目标URL: {report_data['target_info']['url']}")
    print(f"总场景数: {report_data['summary']['total_scenarios']}")
    print(f"发现漏洞: {report_data['summary']['total_vulnerabilities']}")
    print(f"严重程度分布: {report_data['summary']['severity_breakdown']}")
    print(f"修复建议数: {len(report_data['recommendations'])}")
    
    print("\n✅ 报告数据准备完成")


def example_5_end_to_end_workflow():
    """示例5: 端到端工作流程演示"""
    print("\n" + "="*60)
    print("示例5: 端到端工作流程演示")
    print("="*60)
    
    # 1. 目标信息收集
    print("1️⃣ 目标信息收集")
    target_info = {
        'url': 'https://demo.testfire.net',
        'tech_stack': 'Apache + Java',
        'app_type': 'Banking Web Application',
        'endpoints': ['/login', '/bank/main', '/search'],
        'special_features': ['Authentication', 'Money Transfer']
    }
    print(f"   目标: {target_info['url']}")
    print(f"   技术栈: {target_info['tech_stack']}")
    print(f"   端点数: {len(target_info['endpoints'])}")
    
    # 2. 场景生成
    print("\n2️⃣ 测试场景生成")
    scenarios_count = 3
    print(f"   生成场景数: {scenarios_count}")
    print("   场景类型: SQL注入, XSS, 权限提升")
    
    # 3. 扫描执行
    print("\n3️⃣ 扫描执行")
    print("   执行SQL注入测试... ✅ 发现漏洞")
    print("   执行XSS测试... ✅ 发现漏洞")
    print("   执行权限提升测试... ❌ 未发现漏洞")
    
    # 4. 结果分析
    print("\n4️⃣ 结果分析")
    print("   总请求数: 15")
    print("   成功请求: 12")
    print("   发现漏洞: 2")
    print("   成功率: 80%")
    
    # 5. 报告生成
    print("\n5️⃣ 报告生成")
    print("   生成JSON报告... ✅")
    print("   生成HTML报告... ✅")
    print("   生成TXT报告... ✅")
    
    print("\n✅ 端到端工作流程演示完成")


def main():
    """主函数"""
    print("Web渗透测试工具 - 使用示例")
    print("="*60)
    
    try:
        example_1_basic_scenario_generation()
        example_2_mock_qwq_response()
        example_3_web_scanning_simulation()
        example_4_report_generation()
        example_5_end_to_end_workflow()
        
        print("\n" + "="*60)
        print("🎉 所有示例运行完成！")
        print("\n下一步:")
        print("1. 设置真实的API密钥")
        print("2. 运行: python main.py --test-api")
        print("3. 运行: python main.py https://httpbin.org")
        
    except Exception as e:
        logger.error(f"示例运行失败: {e}")
        return 1
    
    return 0


if __name__ == "__main__":
    sys.exit(main())
