"""
Web扫描执行模块
负责执行生成的渗透测试场景
"""

import asyncio
import json
import re
import time
from typing import Dict, List, Optional, Any, Tuple
from urllib.parse import urljoin, urlparse, parse_qs
import aiohttp
import requests
from bs4 import BeautifulSoup
import ssl

from .logger import get_logger

logger = get_logger()


class WebScanner:
    """Web应用扫描器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.session = requests.Session()
        self.setup_session()
        
        # 扫描统计
        self.stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'vulnerabilities_found': 0,
            'start_time': None,
            'end_time': None
        }
    
    def setup_session(self):
        """设置HTTP会话"""
        scanning_config = self.config.get('scanning', {})
        
        # 设置用户代理
        self.session.headers.update({
            'User-Agent': scanning_config.get('user_agent', 'WebPenTestTool/1.0')
        })
        
        # SSL验证设置
        if not scanning_config.get('verify_ssl', True):
            self.session.verify = False
            import urllib3
            urllib3.disable_warnings(urllib3.exceptions.InsecureRequestWarning)
        
        # 重试设置
        from requests.adapters import HTTPAdapter
        from urllib3.util.retry import Retry
        
        retry_strategy = Retry(
            total=scanning_config.get('max_retries', 3),
            backoff_factor=1,
            status_forcelist=[429, 500, 502, 503, 504],
        )
        
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)
    
    def discover_endpoints(self, base_url: str) -> List[str]:
        """发现Web应用端点"""
        logger.info(f"🔍 开始发现端点: {base_url}")
        
        endpoints = set()
        endpoints.add(base_url)
        
        try:
            # 获取主页
            response = self.session.get(base_url, timeout=30)
            if response.status_code == 200:
                soup = BeautifulSoup(response.text, 'html.parser')
                
                # 提取链接
                for link in soup.find_all('a', href=True):
                    href = link['href']
                    full_url = urljoin(base_url, href)
                    if self._is_valid_endpoint(full_url, base_url):
                        endpoints.add(full_url)
                
                # 提取表单
                for form in soup.find_all('form'):
                    action = form.get('action', '')
                    if action:
                        form_url = urljoin(base_url, action)
                        if self._is_valid_endpoint(form_url, base_url):
                            endpoints.add(form_url)
            
            # 常见端点探测
            common_endpoints = [
                '/admin', '/login', '/api', '/api/v1', '/api/v2',
                '/dashboard', '/user', '/users', '/profile',
                '/search', '/upload', '/download', '/config',
                '/robots.txt', '/sitemap.xml', '/.env'
            ]
            
            for endpoint in common_endpoints:
                test_url = urljoin(base_url, endpoint)
                try:
                    response = self.session.head(test_url, timeout=10)
                    if response.status_code not in [404, 403]:
                        endpoints.add(test_url)
                except:
                    pass
            
            logger.info(f"📋 发现 {len(endpoints)} 个端点")
            return list(endpoints)
            
        except Exception as e:
            logger.error(f"❌ 端点发现失败: {e}")
            return [base_url]
    
    def _is_valid_endpoint(self, url: str, base_url: str) -> bool:
        """检查端点是否有效"""
        try:
            parsed_url = urlparse(url)
            parsed_base = urlparse(base_url)
            
            # 同域检查
            if parsed_url.netloc and parsed_url.netloc != parsed_base.netloc:
                return False
            
            # 排除特定模式
            exclude_patterns = self.config.get('security', {}).get('exclude_patterns', [])
            for pattern in exclude_patterns:
                if pattern.lower() in url.lower():
                    return False
            
            # 排除文件扩展名
            exclude_extensions = ['.jpg', '.png', '.gif', '.css', '.js', '.ico', '.pdf']
            if any(url.lower().endswith(ext) for ext in exclude_extensions):
                return False
            
            return True
            
        except Exception:
            return False
    
    def execute_scenario(self, scenario: Dict, target_url: str) -> Dict[str, Any]:
        """执行单个测试场景"""
        logger.log_test_start(target_url, scenario.get('name', 'Unknown'))
        
        self.stats['total_requests'] += 1
        
        try:
            results = {
                'scenario_id': scenario.get('id'),
                'scenario_name': scenario.get('name'),
                'target_url': target_url,
                'success': False,
                'vulnerabilities': [],
                'responses': [],
                'execution_time': 0,
                'error': None
            }
            
            start_time = time.time()
            
            # 执行测试步骤
            for step in scenario.get('test_steps', []):
                step_result = self._execute_test_step(step, target_url, scenario)
                results['responses'].append(step_result)
                
                # 检查是否发现漏洞
                if step_result.get('vulnerability_detected'):
                    results['vulnerabilities'].append(step_result['vulnerability_info'])
                    self.stats['vulnerabilities_found'] += 1
            
            results['execution_time'] = time.time() - start_time
            results['success'] = True
            self.stats['successful_requests'] += 1
            
            # 记录结果
            severity = scenario.get('severity', 'info')
            if results['vulnerabilities']:
                logger.log_vulnerability_found(
                    scenario.get('category', 'Unknown'),
                    target_url,
                    {'vulnerabilities': len(results['vulnerabilities'])}
                )
            
            logger.log_test_result(target_url, scenario.get('name'), 'Completed', severity)
            
            return results
            
        except Exception as e:
            self.stats['failed_requests'] += 1
            logger.error(f"❌ 场景执行失败: {e}")
            return {
                'scenario_id': scenario.get('id'),
                'scenario_name': scenario.get('name'),
                'target_url': target_url,
                'success': False,
                'error': str(e),
                'execution_time': time.time() - start_time if 'start_time' in locals() else 0
            }
    
    def _execute_test_step(self, step: Dict, target_url: str, scenario: Dict) -> Dict[str, Any]:
        """执行单个测试步骤"""
        step_num = step.get('step', 0)
        action = step.get('action', '')
        payload = step.get('payload', '')
        
        logger.debug(f"执行步骤 {step_num}: {action}")
        
        try:
            # 根据动作类型执行不同的测试
            if 'GET' in action.upper():
                response = self._execute_get_request(target_url, payload, step)
            elif 'POST' in action.upper():
                response = self._execute_post_request(target_url, payload, step)
            elif 'SQL' in action.upper():
                response = self._execute_sql_injection_test(target_url, payload, step)
            elif 'XSS' in action.upper():
                response = self._execute_xss_test(target_url, payload, step)
            else:
                response = self._execute_generic_test(target_url, payload, step)
            
            # 分析响应
            vulnerability_info = self._analyze_response(response, step, scenario)
            
            return {
                'step': step_num,
                'action': action,
                'payload': payload,
                'response': response,
                'vulnerability_detected': vulnerability_info is not None,
                'vulnerability_info': vulnerability_info
            }
            
        except Exception as e:
            logger.error(f"❌ 测试步骤执行失败: {e}")
            return {
                'step': step_num,
                'action': action,
                'error': str(e),
                'vulnerability_detected': False
            }
    
    def _execute_get_request(self, url: str, payload: str, step: Dict) -> Dict[str, Any]:
        """执行GET请求测试"""
        if payload:
            # 将载荷添加到URL参数中
            separator = '&' if '?' in url else '?'
            test_url = f"{url}{separator}{payload}"
        else:
            test_url = url
        
        start_time = time.time()
        response = self.session.get(test_url, timeout=30)
        response_time = (time.time() - start_time) * 1000
        
        return {
            'url': test_url,
            'method': 'GET',
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'content': response.text,
            'response_time': response_time,
            'content_length': len(response.content)
        }
    
    def _execute_post_request(self, url: str, payload: str, step: Dict) -> Dict[str, Any]:
        """执行POST请求测试"""
        # 解析载荷为POST数据
        try:
            if payload.startswith('{'):
                data = json.loads(payload)
                headers = {'Content-Type': 'application/json'}
            else:
                # 假设是表单数据
                data = dict(param.split('=') for param in payload.split('&') if '=' in param)
                headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        except:
            data = {'test_param': payload}
            headers = {'Content-Type': 'application/x-www-form-urlencoded'}
        
        start_time = time.time()
        response = self.session.post(url, data=data, headers=headers, timeout=30)
        response_time = (time.time() - start_time) * 1000
        
        return {
            'url': url,
            'method': 'POST',
            'data': data,
            'status_code': response.status_code,
            'headers': dict(response.headers),
            'content': response.text,
            'response_time': response_time,
            'content_length': len(response.content)
        }
    
    def _execute_sql_injection_test(self, url: str, payload: str, step: Dict) -> Dict[str, Any]:
        """执行SQL注入测试"""
        # SQL注入特定的测试逻辑
        return self._execute_get_request(url, f"id={payload}", step)
    
    def _execute_xss_test(self, url: str, payload: str, step: Dict) -> Dict[str, Any]:
        """执行XSS测试"""
        # XSS特定的测试逻辑
        return self._execute_get_request(url, f"search={payload}", step)
    
    def _execute_generic_test(self, url: str, payload: str, step: Dict) -> Dict[str, Any]:
        """执行通用测试"""
        return self._execute_get_request(url, payload, step)
    
    def _analyze_response(self, response: Dict, step: Dict, scenario: Dict) -> Optional[Dict]:
        """分析响应以检测漏洞"""
        content = response.get('content', '')
        status_code = response.get('status_code', 0)
        headers = response.get('headers', {})
        response_time = response.get('response_time', 0)
        
        vulnerability_indicators = {
            'sql_injection': [
                'mysql_fetch_array', 'ORA-01756', 'Microsoft OLE DB',
                'SQLServer JDBC Driver', 'PostgreSQL query failed',
                'syntax error', 'mysql_num_rows', 'Warning: mysql'
            ],
            'xss': [
                '<script>', 'javascript:', 'onerror=', 'onload=',
                'alert(', 'document.cookie', 'eval('
            ],
            'path_traversal': [
                'root:x:', '[boot loader]', 'Windows Registry Editor',
                '/etc/passwd', 'Directory of C:\\'
            ],
            'information_disclosure': [
                'stack trace', 'debug mode', 'exception',
                'error occurred', 'warning:', 'notice:'
            ]
        }
        
        # 检查各种漏洞指标
        for vuln_type, indicators in vulnerability_indicators.items():
            for indicator in indicators:
                if indicator.lower() in content.lower():
                    return {
                        'type': vuln_type,
                        'indicator': indicator,
                        'confidence': 'high',
                        'evidence': content[:500],
                        'severity': scenario.get('severity', 'medium')
                    }
        
        # 检查时间盲注
        if response_time > 5000:  # 5秒以上
            if any(keyword in step.get('payload', '').lower() for keyword in ['sleep', 'waitfor', 'delay']):
                return {
                    'type': 'time_based_blind_injection',
                    'indicator': f'Response time: {response_time}ms',
                    'confidence': 'medium',
                    'evidence': f'Delayed response: {response_time}ms',
                    'severity': 'high'
                }
        
        # 检查错误状态码
        if status_code == 500:
            return {
                'type': 'server_error',
                'indicator': 'HTTP 500 Internal Server Error',
                'confidence': 'low',
                'evidence': f'Status code: {status_code}',
                'severity': 'low'
            }
        
        return None
    
    def get_scan_statistics(self) -> Dict[str, Any]:
        """获取扫描统计信息"""
        if self.stats['start_time'] and self.stats['end_time']:
            duration = self.stats['end_time'] - self.stats['start_time']
        else:
            duration = 0
        
        return {
            'total_requests': self.stats['total_requests'],
            'successful_requests': self.stats['successful_requests'],
            'failed_requests': self.stats['failed_requests'],
            'vulnerabilities_found': self.stats['vulnerabilities_found'],
            'success_rate': (self.stats['successful_requests'] / max(self.stats['total_requests'], 1)) * 100,
            'scan_duration': duration,
            'requests_per_second': self.stats['total_requests'] / max(duration, 1)
        }
