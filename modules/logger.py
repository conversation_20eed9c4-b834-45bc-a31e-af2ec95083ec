"""
全局日志系统模块
提供统一的日志记录功能，支持文件和控制台输出
"""

import logging
import logging.handlers
import os
import sys
from datetime import datetime
from pathlib import Path
from typing import Optional

from rich.console import Console
from rich.logging import RichHandler
from rich.text import Text
import yaml


class PenTestLogger:
    """渗透测试工具专用日志器"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.console = Console()
        self.config = self._load_config(config_path)
        self.logger = self._setup_logger()
        
    def _load_config(self, config_path: str) -> dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return yaml.safe_load(f)
        except FileNotFoundError:
            return self._default_config()
    
    def _default_config(self) -> dict:
        """默认配置"""
        return {
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
                'file_max_size': '10MB',
                'backup_count': 5,
                'console_output': True
            }
        }
    
    def _setup_logger(self) -> logging.Logger:
        """设置日志器"""
        # 创建日志目录
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # 创建主日志器
        logger = logging.getLogger("WebPenTest")
        logger.setLevel(getattr(logging, self.config['logging']['level']))
        
        # 清除现有处理器
        logger.handlers.clear()
        
        # 文件处理器
        log_file = log_dir / f"pentest_{datetime.now().strftime('%Y%m%d')}.log"
        file_handler = logging.handlers.RotatingFileHandler(
            filename=log_file,
            maxBytes=self._parse_size(self.config['logging']['file_max_size']),
            backupCount=self.config['logging']['backup_count'],
            encoding='utf-8'
        )
        file_formatter = logging.Formatter(self.config['logging']['format'])
        file_handler.setFormatter(file_formatter)
        logger.addHandler(file_handler)
        
        # 控制台处理器（使用Rich）
        if self.config['logging']['console_output']:
            console_handler = RichHandler(
                console=self.console,
                show_time=True,
                show_path=False,
                rich_tracebacks=True
            )
            console_handler.setFormatter(logging.Formatter("%(message)s"))
            logger.addHandler(console_handler)
        
        return logger
    
    def _parse_size(self, size_str: str) -> int:
        """解析大小字符串为字节数"""
        size_str = size_str.upper()
        if size_str.endswith('KB'):
            return int(size_str[:-2]) * 1024
        elif size_str.endswith('MB'):
            return int(size_str[:-2]) * 1024 * 1024
        elif size_str.endswith('GB'):
            return int(size_str[:-2]) * 1024 * 1024 * 1024
        else:
            return int(size_str)
    
    def info(self, message: str, extra_data: Optional[dict] = None):
        """记录信息日志"""
        if extra_data:
            message = f"{message} | {extra_data}"
        self.logger.info(message)
    
    def warning(self, message: str, extra_data: Optional[dict] = None):
        """记录警告日志"""
        if extra_data:
            message = f"{message} | {extra_data}"
        self.logger.warning(message)
    
    def error(self, message: str, extra_data: Optional[dict] = None):
        """记录错误日志"""
        if extra_data:
            message = f"{message} | {extra_data}"
        self.logger.error(message)
    
    def critical(self, message: str, extra_data: Optional[dict] = None):
        """记录严重错误日志"""
        if extra_data:
            message = f"{message} | {extra_data}"
        self.logger.critical(message)
    
    def debug(self, message: str, extra_data: Optional[dict] = None):
        """记录调试日志"""
        if extra_data:
            message = f"{message} | {extra_data}"
        self.logger.debug(message)
    
    def log_test_start(self, target: str, scenario: str):
        """记录测试开始"""
        self.info(f"🚀 开始测试 | 目标: {target} | 场景: {scenario}")
    
    def log_test_result(self, target: str, scenario: str, result: str, severity: str = "info"):
        """记录测试结果"""
        emoji = {"low": "🟡", "medium": "🟠", "high": "🔴", "critical": "💀"}.get(severity, "ℹ️")
        self.info(f"{emoji} 测试结果 | 目标: {target} | 场景: {scenario} | 结果: {result}")
    
    def log_vulnerability_found(self, vuln_type: str, target: str, details: dict):
        """记录发现的漏洞"""
        self.warning(f"🔍 发现漏洞 | 类型: {vuln_type} | 目标: {target} | 详情: {details}")
    
    def log_scan_progress(self, current: int, total: int, target: str):
        """记录扫描进度"""
        progress = (current / total) * 100
        self.info(f"📊 扫描进度 | {progress:.1f}% ({current}/{total}) | 目标: {target}")


# 全局日志器实例
logger = PenTestLogger()


# 兼容性函数
def get_logger() -> PenTestLogger:
    """获取全局日志器实例"""
    return logger
