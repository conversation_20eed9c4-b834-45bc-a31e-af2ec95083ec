"""
报告生成模块
生成多种格式的渗透测试报告
"""

import json
import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any
from jinja2 import Template
import yaml

from .logger import get_logger

logger = get_logger()


class ReportGenerator:
    """渗透测试报告生成器"""
    
    def __init__(self, config: Dict):
        self.config = config
        self.report_config = config.get('reporting', {})
        self.export_path = Path(self.report_config.get('export_path', './reports'))
        self.export_path.mkdir(exist_ok=True)
    
    def generate_report(self, scan_results: Dict[str, Any], target_info: Dict) -> Dict[str, str]:
        """生成完整的测试报告"""
        logger.info("📊 开始生成渗透测试报告")
        
        # 准备报告数据
        report_data = self._prepare_report_data(scan_results, target_info)
        
        # 生成不同格式的报告
        generated_files = {}
        formats = self.report_config.get('format', ['json'])
        
        for format_type in formats:
            try:
                if format_type == 'json':
                    file_path = self._generate_json_report(report_data)
                elif format_type == 'html':
                    file_path = self._generate_html_report(report_data)
                elif format_type == 'txt':
                    file_path = self._generate_text_report(report_data)
                else:
                    logger.warning(f"⚠️ 不支持的报告格式: {format_type}")
                    continue
                
                generated_files[format_type] = file_path
                logger.info(f"✅ {format_type.upper()}报告生成成功: {file_path}")
                
            except Exception as e:
                logger.error(f"❌ {format_type.upper()}报告生成失败: {e}")
        
        logger.info(f"📋 报告生成完成，共生成 {len(generated_files)} 个文件")
        return generated_files
    
    def _prepare_report_data(self, scan_results: Dict, target_info: Dict) -> Dict[str, Any]:
        """准备报告数据"""
        timestamp = datetime.now()
        
        # 统计漏洞
        vulnerabilities = []
        total_scenarios = 0
        successful_scenarios = 0
        
        for result in scan_results.get('results', []):
            total_scenarios += 1
            if result.get('success'):
                successful_scenarios += 1
            
            for vuln in result.get('vulnerabilities', []):
                vulnerabilities.append({
                    'scenario': result.get('scenario_name'),
                    'target': result.get('target_url'),
                    'type': vuln.get('type'),
                    'severity': vuln.get('severity'),
                    'confidence': vuln.get('confidence'),
                    'evidence': vuln.get('evidence', '')[:200] + '...' if len(vuln.get('evidence', '')) > 200 else vuln.get('evidence', ''),
                    'indicator': vuln.get('indicator')
                })
        
        # 按严重程度分类
        severity_counts = {'critical': 0, 'high': 0, 'medium': 0, 'low': 0}
        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'low')
            if severity in severity_counts:
                severity_counts[severity] += 1
        
        return {
            'metadata': {
                'report_id': f"pentest_{timestamp.strftime('%Y%m%d_%H%M%S')}",
                'generated_at': timestamp.isoformat(),
                'tool_version': '1.0.0',
                'scan_duration': scan_results.get('scan_duration', 0)
            },
            'target_info': target_info,
            'summary': {
                'total_scenarios': total_scenarios,
                'successful_scenarios': successful_scenarios,
                'total_vulnerabilities': len(vulnerabilities),
                'severity_breakdown': severity_counts,
                'success_rate': (successful_scenarios / max(total_scenarios, 1)) * 100
            },
            'vulnerabilities': vulnerabilities,
            'scan_results': scan_results,
            'statistics': scan_results.get('statistics', {}),
            'recommendations': self._generate_recommendations(vulnerabilities)
        }
    
    def _generate_recommendations(self, vulnerabilities: List[Dict]) -> List[Dict]:
        """生成修复建议"""
        recommendations = []
        
        # 基于发现的漏洞类型生成建议
        vuln_types = set(vuln.get('type') for vuln in vulnerabilities)
        
        recommendation_map = {
            'sql_injection': {
                'title': 'SQL注入防护',
                'priority': 'critical',
                'description': '使用参数化查询和输入验证来防止SQL注入攻击',
                'actions': [
                    '使用预编译语句(Prepared Statements)',
                    '实施严格的输入验证',
                    '使用最小权限原则配置数据库用户',
                    '启用数据库查询日志监控'
                ]
            },
            'xss': {
                'title': 'XSS防护',
                'priority': 'high',
                'description': '实施输出编码和内容安全策略来防止XSS攻击',
                'actions': [
                    '对所有用户输入进行HTML编码',
                    '实施内容安全策略(CSP)',
                    '使用安全的JavaScript框架',
                    '验证和过滤用户输入'
                ]
            },
            'path_traversal': {
                'title': '路径遍历防护',
                'priority': 'high',
                'description': '限制文件访问权限并验证文件路径',
                'actions': [
                    '使用白名单验证文件路径',
                    '限制Web应用的文件系统访问权限',
                    '避免直接使用用户输入构造文件路径',
                    '实施严格的访问控制'
                ]
            },
            'information_disclosure': {
                'title': '信息泄露防护',
                'priority': 'medium',
                'description': '减少错误信息暴露和敏感信息泄露',
                'actions': [
                    '配置自定义错误页面',
                    '禁用详细错误信息输出',
                    '移除调试信息和注释',
                    '实施适当的日志记录策略'
                ]
            }
        }
        
        for vuln_type in vuln_types:
            if vuln_type in recommendation_map:
                recommendations.append(recommendation_map[vuln_type])
        
        # 通用安全建议
        recommendations.append({
            'title': '通用安全加固',
            'priority': 'medium',
            'description': '实施基础的Web应用安全措施',
            'actions': [
                '启用HTTPS并配置安全的TLS设置',
                '实施适当的会话管理',
                '配置安全HTTP头部',
                '定期更新依赖组件',
                '实施访问日志和监控'
            ]
        })
        
        return recommendations
    
    def _generate_json_report(self, report_data: Dict) -> str:
        """生成JSON格式报告"""
        filename = f"{report_data['metadata']['report_id']}.json"
        file_path = self.export_path / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False, default=str)
        
        return str(file_path)
    
    def _generate_html_report(self, report_data: Dict) -> str:
        """生成HTML格式报告"""
        html_template = """
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Web渗透测试报告 - {{ metadata.report_id }}</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; line-height: 1.6; }
        .header { background: #2c3e50; color: white; padding: 20px; border-radius: 5px; }
        .summary { background: #ecf0f1; padding: 15px; margin: 20px 0; border-radius: 5px; }
        .vulnerability { border: 1px solid #ddd; margin: 10px 0; padding: 15px; border-radius: 5px; }
        .critical { border-left: 5px solid #e74c3c; }
        .high { border-left: 5px solid #f39c12; }
        .medium { border-left: 5px solid #f1c40f; }
        .low { border-left: 5px solid #27ae60; }
        .recommendation { background: #d5f4e6; padding: 15px; margin: 10px 0; border-radius: 5px; }
        table { width: 100%; border-collapse: collapse; margin: 20px 0; }
        th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
        th { background-color: #f2f2f2; }
        .severity-critical { color: #e74c3c; font-weight: bold; }
        .severity-high { color: #f39c12; font-weight: bold; }
        .severity-medium { color: #f1c40f; font-weight: bold; }
        .severity-low { color: #27ae60; font-weight: bold; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Web渗透测试报告</h1>
        <p>报告ID: {{ metadata.report_id }}</p>
        <p>生成时间: {{ metadata.generated_at }}</p>
        <p>目标: {{ target_info.url }}</p>
    </div>

    <div class="summary">
        <h2>执行摘要</h2>
        <table>
            <tr><td>测试场景总数</td><td>{{ summary.total_scenarios }}</td></tr>
            <tr><td>成功执行场景</td><td>{{ summary.successful_scenarios }}</td></tr>
            <tr><td>发现漏洞总数</td><td>{{ summary.total_vulnerabilities }}</td></tr>
            <tr><td>成功率</td><td>{{ "%.1f"|format(summary.success_rate) }}%</td></tr>
        </table>
        
        <h3>漏洞严重程度分布</h3>
        <table>
            <tr><td class="severity-critical">严重</td><td>{{ summary.severity_breakdown.critical }}</td></tr>
            <tr><td class="severity-high">高危</td><td>{{ summary.severity_breakdown.high }}</td></tr>
            <tr><td class="severity-medium">中危</td><td>{{ summary.severity_breakdown.medium }}</td></tr>
            <tr><td class="severity-low">低危</td><td>{{ summary.severity_breakdown.low }}</td></tr>
        </table>
    </div>

    <h2>发现的漏洞</h2>
    {% for vuln in vulnerabilities %}
    <div class="vulnerability {{ vuln.severity }}">
        <h3>{{ vuln.type }} - {{ vuln.scenario }}</h3>
        <p><strong>目标:</strong> {{ vuln.target }}</p>
        <p><strong>严重程度:</strong> <span class="severity-{{ vuln.severity }}">{{ vuln.severity }}</span></p>
        <p><strong>置信度:</strong> {{ vuln.confidence }}</p>
        <p><strong>指标:</strong> {{ vuln.indicator }}</p>
        <p><strong>证据:</strong> {{ vuln.evidence }}</p>
    </div>
    {% endfor %}

    <h2>修复建议</h2>
    {% for rec in recommendations %}
    <div class="recommendation">
        <h3>{{ rec.title }} (优先级: {{ rec.priority }})</h3>
        <p>{{ rec.description }}</p>
        <ul>
        {% for action in rec.actions %}
            <li>{{ action }}</li>
        {% endfor %}
        </ul>
    </div>
    {% endfor %}

    <div style="margin-top: 50px; text-align: center; color: #7f8c8d;">
        <p>报告由 WebPenTestTool v1.0 生成</p>
    </div>
</body>
</html>
        """
        
        template = Template(html_template)
        html_content = template.render(**report_data)
        
        filename = f"{report_data['metadata']['report_id']}.html"
        file_path = self.export_path / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        return str(file_path)
    
    def _generate_text_report(self, report_data: Dict) -> str:
        """生成文本格式报告"""
        lines = []
        lines.append("=" * 60)
        lines.append("Web渗透测试报告")
        lines.append("=" * 60)
        lines.append(f"报告ID: {report_data['metadata']['report_id']}")
        lines.append(f"生成时间: {report_data['metadata']['generated_at']}")
        lines.append(f"目标: {report_data['target_info'].get('url', 'N/A')}")
        lines.append("")
        
        # 摘要
        lines.append("执行摘要")
        lines.append("-" * 20)
        summary = report_data['summary']
        lines.append(f"测试场景总数: {summary['total_scenarios']}")
        lines.append(f"成功执行场景: {summary['successful_scenarios']}")
        lines.append(f"发现漏洞总数: {summary['total_vulnerabilities']}")
        lines.append(f"成功率: {summary['success_rate']:.1f}%")
        lines.append("")
        
        # 漏洞详情
        lines.append("发现的漏洞")
        lines.append("-" * 20)
        for i, vuln in enumerate(report_data['vulnerabilities'], 1):
            lines.append(f"{i}. {vuln['type']} ({vuln['severity']})")
            lines.append(f"   场景: {vuln['scenario']}")
            lines.append(f"   目标: {vuln['target']}")
            lines.append(f"   指标: {vuln['indicator']}")
            lines.append("")
        
        # 修复建议
        lines.append("修复建议")
        lines.append("-" * 20)
        for i, rec in enumerate(report_data['recommendations'], 1):
            lines.append(f"{i}. {rec['title']} (优先级: {rec['priority']})")
            lines.append(f"   {rec['description']}")
            for action in rec['actions']:
                lines.append(f"   - {action}")
            lines.append("")
        
        filename = f"{report_data['metadata']['report_id']}.txt"
        file_path = self.export_path / filename
        
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write('\n'.join(lines))
        
        return str(file_path)
