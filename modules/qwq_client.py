"""
QwQ-32B模型客户端模块
负责与QwQ-32B模型API的交互
"""

import json
import os
import time
from typing import Dict, List, Optional, Any
import asyncio
import aiohttp
import requests
from dotenv import load_dotenv

from .logger import get_logger

# 加载环境变量
load_dotenv()

logger = get_logger()


class QwQClient:
    """QwQ-32B模型API客户端"""
    
    def __init__(self, config: Dict):
        self.config = config
        # self.api_key = os.getenv('QWQ_API_KEY')
        self.api_key = "sk-jOLbXwkWAkp5BMVJ88AeC18dEd4547Ab8680BcAf3fFe39D3"
        self.api_endpoint = os.getenv('QWQ_API_ENDPOINT', config['model']['api_endpoint'])
        self.model_name = config['model']['name']
        self.max_tokens = config['model']['max_tokens']
        self.temperature = config['model']['temperature']
        self.timeout = config['model']['timeout']
        
        if not self.api_key:
            logger.error("QwQ API密钥未设置，请在.env文件中设置QWQ_API_KEY")
            raise ValueError("QwQ API密钥未设置")
    
    def _prepare_headers(self) -> Dict[str, str]:
        """准备请求头"""
        return {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json',
            'User-Agent': 'WebPenTestTool/1.0'
        }
    
    def _prepare_payload(self, prompt: str, system_prompt: Optional[str] = None) -> Dict[str, Any]:
        """准备请求载荷"""
        messages = []
        
        if system_prompt:
            messages.append({
                "role": "system",
                "content": system_prompt
            })
        
        messages.append({
            "role": "user", 
            "content": prompt
        })
        
        return {
            "model": self.model_name,
            "messages": messages,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "stream": False
        }
    
    def generate_scenarios(self, prompt: str, system_prompt: Optional[str] = None) -> Optional[Dict]:
        """生成渗透测试场景"""
        logger.info("🤖 开始调用QwQ-32B生成测试场景")
        
        try:
            headers = self._prepare_headers()
            payload = self._prepare_payload(prompt, system_prompt)
            
            logger.debug(f"API请求载荷: {json.dumps(payload, indent=2, ensure_ascii=False)}")
            
            response = requests.post(
                self.api_endpoint,
                headers=headers,
                json=payload,
                timeout=self.timeout
            )
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                
                logger.info("✅ QwQ-32B响应成功")
                logger.debug(f"模型响应: {content[:500]}...")
                
                # 尝试解析JSON响应
                try:
                    scenarios = json.loads(content)
                    logger.info(f"📋 成功生成 {len(scenarios.get('scenarios', []))} 个测试场景")
                    return scenarios
                except json.JSONDecodeError as e:
                    logger.warning(f"⚠️ JSON解析失败，返回原始文本: {e}")
                    return {"raw_response": content}
            
            else:
                logger.error(f"❌ API请求失败: {response.status_code} - {response.text}")
                return None
                
        except requests.exceptions.Timeout:
            logger.error("⏰ API请求超时")
            return None
        except requests.exceptions.RequestException as e:
            logger.error(f"🔌 网络请求异常: {e}")
            return None
        except Exception as e:
            logger.error(f"💥 未知错误: {e}")
            return None
    
    async def generate_scenarios_async(self, prompt: str, system_prompt: Optional[str] = None) -> Optional[Dict]:
        """异步生成渗透测试场景"""
        logger.info("🤖 开始异步调用QwQ-32B生成测试场景")
        
        try:
            headers = self._prepare_headers()
            payload = self._prepare_payload(prompt, system_prompt)
            
            async with aiohttp.ClientSession() as session:
                async with session.post(
                    self.api_endpoint,
                    headers=headers,
                    json=payload,
                    timeout=aiohttp.ClientTimeout(total=self.timeout)
                ) as response:
                    
                    if response.status == 200:
                        result = await response.json()
                        content = result['choices'][0]['message']['content']
                        
                        logger.info("✅ QwQ-32B异步响应成功")
                        
                        try:
                            scenarios = json.loads(content)
                            logger.info(f"📋 成功生成 {len(scenarios.get('scenarios', []))} 个测试场景")
                            return scenarios
                        except json.JSONDecodeError as e:
                            logger.warning(f"⚠️ JSON解析失败，返回原始文本: {e}")
                            return {"raw_response": content}
                    else:
                        error_text = await response.text()
                        logger.error(f"❌ API请求失败: {response.status} - {error_text}")
                        return None
                        
        except asyncio.TimeoutError:
            logger.error("⏰ 异步API请求超时")
            return None
        except aiohttp.ClientError as e:
            logger.error(f"🔌 异步网络请求异常: {e}")
            return None
        except Exception as e:
            logger.error(f"💥 异步请求未知错误: {e}")
            return None
    
    def analyze_response(self, response_data: Dict, analysis_prompt: str) -> Optional[Dict]:
        """分析Web响应数据"""
        logger.info("🔍 开始分析Web响应数据")
        
        try:
            # 将响应数据嵌入到提示词中
            full_prompt = f"{analysis_prompt}\n\n响应数据：\n{json.dumps(response_data, indent=2, ensure_ascii=False)}"
            
            result = self.generate_scenarios(full_prompt)
            
            if result:
                logger.info("✅ 响应分析完成")
                return result
            else:
                logger.error("❌ 响应分析失败")
                return None
                
        except Exception as e:
            logger.error(f"💥 响应分析异常: {e}")
            return None
    
    def test_connection(self) -> bool:
        """测试API连接"""
        logger.info("🔗 测试QwQ-32B API连接")
        
        try:
            test_prompt = "请简单回复'连接测试成功'"
            result = self.generate_scenarios(test_prompt)
            
            if result:
                logger.info("✅ API连接测试成功")
                return True
            else:
                logger.error("❌ API连接测试失败")
                return False
                
        except Exception as e:
            logger.error(f"💥 API连接测试异常: {e}")
            return False
    
    def get_model_info(self) -> Dict[str, Any]:
        """获取模型信息"""
        return {
            "model_name": self.model_name,
            "api_endpoint": self.api_endpoint,
            "max_tokens": self.max_tokens,
            "temperature": self.temperature,
            "timeout": self.timeout,
            "api_key_configured": bool(self.api_key)
        }
