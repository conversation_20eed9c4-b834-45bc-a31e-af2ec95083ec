# Web渗透测试工具配置文件

# QwQ-32B模型配置
model:
  name: "QwQ-32B"
  api_endpoint: "http://model.qmai.cn:23000/v1/chat/completions"  # 可替换为其他API端点
  api_key: "sk-jOLbXwkWAkp5BMVJ88AeC18dEd4547Ab8680BcAf3fFe39D3"  # 在.env文件中设置
  max_tokens: 4096
  temperature: 0.7
  timeout: 360

# 日志配置
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_max_size: "10MB"
  backup_count: 5
  console_output: true

# 扫描配置
scanning:
  max_concurrent_requests: 10
  request_timeout: 30
  user_agent: "WebPenTestTool/1.0"
  follow_redirects: true
  verify_ssl: false
  max_retries: 3

# 测试场景配置
scenarios:
  max_scenarios_per_target: 5
  include_owasp_top10: true
  custom_payloads: true
  severity_levels: ["low", "medium", "high", "critical"]

# 报告配置
reporting:
  format: ["json", "html", "txt"]
  include_screenshots: false
  detailed_logs: true
  export_path: "./reports"

# 安全配置
security:
  rate_limiting: true
  requests_per_second: 5
  respect_robots_txt: true
  exclude_patterns:
    - "logout"
    - "delete"
    - "remove"
    - "admin/users/delete"
