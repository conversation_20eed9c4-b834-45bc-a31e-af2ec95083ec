2025-07-28 17:46:52,077 - WebPenTest - INFO - ✅ 配置文件加载成功: config/config.yaml
2025-07-28 17:46:52,079 - WebPenTest - ERROR - QwQ API密钥未设置，请在.env文件中设置QWQ_API_KEY
2025-07-28 17:46:52,079 - WebPenTest - ERROR - 💥 程序异常: QwQ API密钥未设置
2025-07-28 17:47:50,532 - WebPenTest - INFO - ✅ 配置文件加载成功: config/config.yaml
2025-07-28 17:47:50,532 - WebPenTest - ERROR - QwQ API密钥未设置，请在.env文件中设置QWQ_API_KEY
2025-07-28 17:47:50,532 - WebPenTest - ERROR - 💥 程序异常: QwQ API密钥未设置
2025-07-28 17:48:10,689 - WebPenTest - INFO - ✅ 配置文件加载成功: config/config.yaml
2025-07-28 17:48:10,694 - WebPenTest - ERROR - QwQ API密钥未设置，请在.env文件中设置QWQ_API_KEY
2025-07-28 17:48:10,695 - WebPenTest - ERROR - 💥 程序异常: QwQ API密钥未设置
2025-07-28 17:49:27,683 - WebPenTest - INFO - ✅ 配置文件加载成功: config/config.yaml
2025-07-28 17:49:27,688 - WebPenTest - ERROR - QwQ API密钥未设置，请在.env文件中设置QWQ_API_KEY
2025-07-28 17:49:27,689 - WebPenTest - ERROR - 💥 程序异常: QwQ API密钥未设置
2025-07-28 17:50:48,538 - WebPenTest - INFO - ✅ 配置文件加载成功: config/config.yaml
2025-07-28 17:50:48,542 - WebPenTest - ERROR - QwQ API密钥未设置，请在.env文件中设置QWQ_API_KEY
2025-07-28 17:50:48,543 - WebPenTest - ERROR - 💥 程序异常: QwQ API密钥未设置
2025-07-28 17:51:40,374 - WebPenTest - INFO - ✅ 配置文件加载成功: config/config.yaml
2025-07-28 17:51:40,376 - WebPenTest - INFO - 🚀 Web渗透测试工具初始化完成
2025-07-28 17:51:40,376 - WebPenTest - INFO - 🚀 开始完整的渗透测试流程
2025-07-28 17:51:40,376 - WebPenTest - INFO - ✅ 目标URL验证通过: https://console.qmai.co/login
2025-07-28 17:51:40,376 - WebPenTest - INFO - 🔗 测试QwQ-32B API连接
2025-07-28 17:51:40,376 - WebPenTest - INFO - 🤖 开始调用QwQ-32B生成测试场景
2025-07-28 17:51:48,956 - WebPenTest - INFO - ✅ QwQ-32B响应成功
2025-07-28 17:51:48,959 - WebPenTest - WARNING - ⚠️ JSON解析失败，返回原始文本: Expecting value: line 1 column 1 (char 0)
2025-07-28 17:51:48,962 - WebPenTest - INFO - ✅ API连接测试成功
2025-07-28 17:51:48,963 - WebPenTest - INFO - 🔍 开始收集目标信息: https://console.qmai.co/login
2025-07-28 17:51:48,965 - WebPenTest - INFO - 🔍 开始发现端点: https://console.qmai.co/login
2025-07-28 17:51:51,167 - WebPenTest - INFO - 📋 发现 16 个端点
2025-07-28 17:51:51,292 - WebPenTest - INFO - 📋 目标信息收集完成: 16 个端点
2025-07-28 17:51:51,295 - WebPenTest - INFO - 🤖 开始生成渗透测试场景
2025-07-28 17:51:51,298 - WebPenTest - INFO - 🤖 开始调用QwQ-32B生成测试场景
2025-07-28 17:53:00,869 - WebPenTest - INFO - ✅ QwQ-32B响应成功
2025-07-28 17:53:00,871 - WebPenTest - WARNING - ⚠️ JSON解析失败，返回原始文本: Expecting value: line 1 column 1 (char 0)
2025-07-28 17:53:00,873 - WebPenTest - WARNING - ⚠️ 收到原始响应，尝试手动解析
2025-07-28 17:53:00,875 - WebPenTest - INFO - 🎯 开始执行渗透测试: 1 个场景
2025-07-28 17:53:00,878 - WebPenTest - INFO - 📊 扫描进度 | 100.0% (1/1) | 目标: https://console.qmai.co/login
2025-07-28 17:53:00,881 - WebPenTest - INFO - 🚀 开始测试 | 目标: https://console.qmai.co/login/ | 场景: 基础SQL注入测试
2025-07-28 17:53:01,002 - WebPenTest - WARNING - 🔍 发现漏洞 | 类型: A03_Injection | 目标: https://console.qmai.co/login/ | 详情: {'vulnerabilities': 1}
2025-07-28 17:53:01,005 - WebPenTest - INFO - 🔴 测试结果 | 目标: https://console.qmai.co/login/ | 场景: 基础SQL注入测试 | 结果: Completed
2025-07-28 17:53:01,208 - WebPenTest - INFO - ✅ 渗透测试执行完成，耗时 0.33 秒
2025-07-28 17:53:01,210 - WebPenTest - INFO - 📊 开始生成渗透测试报告
2025-07-28 17:53:01,213 - WebPenTest - INFO - ✅ JSON报告生成成功: reports\pentest_20250728_175301.json
2025-07-28 17:53:01,222 - WebPenTest - INFO - ✅ HTML报告生成成功: reports\pentest_20250728_175301.html
2025-07-28 17:53:01,222 - WebPenTest - INFO - ✅ TXT报告生成成功: reports\pentest_20250728_175301.txt
2025-07-28 17:53:01,226 - WebPenTest - INFO - 📋 报告生成完成，共生成 3 个文件
2025-07-28 17:53:01,231 - WebPenTest - INFO - 🎉 渗透测试流程完成
