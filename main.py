#!/usr/bin/env python3
"""
Web渗透测试工具主程序
基于QwQ-32B模型生成和执行渗透测试场景
"""

import argparse
import asyncio
import json
import sys
import time
from pathlib import Path
from typing import Dict, List, Optional
import yaml
from urllib.parse import urlparse

# 导入自定义模块
from modules.logger import get_logger
from modules.qwq_client import QwQClient
from modules.web_scanner import WebScanner
from modules.report_generator import ReportGenerator
from prompts.pentest_prompts import get_prompt_generator

logger = get_logger()


class WebPenTestTool:
    """Web渗透测试工具主类"""
    
    def __init__(self, config_path: str = "config/config.yaml"):
        self.config = self._load_config(config_path)
        self.qwq_client = QwQClient(self.config)
        self.web_scanner = WebScanner(self.config)
        self.report_generator = ReportGenerator(self.config)
        self.prompt_generator = get_prompt_generator()
        
        logger.info("🚀 Web渗透测试工具初始化完成")
    
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                config = yaml.safe_load(f)
            logger.info(f"✅ 配置文件加载成功: {config_path}")
            return config
        except FileNotFoundError:
            logger.error(f"❌ 配置文件未找到: {config_path}")
            sys.exit(1)
        except yaml.YAMLError as e:
            logger.error(f"❌ 配置文件格式错误: {e}")
            sys.exit(1)
    
    def validate_target(self, target_url: str) -> bool:
        """验证目标URL"""
        try:
            parsed = urlparse(target_url)
            if not parsed.scheme or not parsed.netloc:
                logger.error(f"❌ 无效的URL格式: {target_url}")
                return False
            
            if parsed.scheme not in ['http', 'https']:
                logger.error(f"❌ 不支持的协议: {parsed.scheme}")
                return False
            
            logger.info(f"✅ 目标URL验证通过: {target_url}")
            return True
            
        except Exception as e:
            logger.error(f"❌ URL验证失败: {e}")
            return False
    
    def gather_target_info(self, target_url: str) -> Dict:
        """收集目标信息"""
        logger.info(f"🔍 开始收集目标信息: {target_url}")
        
        target_info = {
            'url': target_url,
            'tech_stack': 'Unknown',
            'app_type': 'Web Application',
            'endpoints': [],
            'special_features': []
        }
        
        try:
            # 发现端点
            endpoints = self.web_scanner.discover_endpoints(target_url)
            target_info['endpoints'] = endpoints[:10]  # 限制数量
            
            # 简单的技术栈检测
            response = self.web_scanner.session.get(target_url, timeout=30)
            headers = response.headers
            
            # 检测服务器类型
            server = headers.get('Server', '').lower()
            if 'apache' in server:
                target_info['tech_stack'] = 'Apache'
            elif 'nginx' in server:
                target_info['tech_stack'] = 'Nginx'
            elif 'iis' in server:
                target_info['tech_stack'] = 'IIS'
            
            # 检测框架
            if 'x-powered-by' in headers:
                powered_by = headers['x-powered-by'].lower()
                if 'php' in powered_by:
                    target_info['tech_stack'] += ' + PHP'
                elif 'asp.net' in powered_by:
                    target_info['tech_stack'] += ' + ASP.NET'
            
            # 检测特殊功能
            content = response.text.lower()
            if 'login' in content:
                target_info['special_features'].append('Authentication')
            if 'upload' in content:
                target_info['special_features'].append('File Upload')
            if 'search' in content:
                target_info['special_features'].append('Search Function')
            
            logger.info(f"📋 目标信息收集完成: {len(endpoints)} 个端点")
            return target_info
            
        except Exception as e:
            logger.error(f"❌ 目标信息收集失败: {e}")
            return target_info
    
    def generate_test_scenarios(self, target_info: Dict) -> Optional[List[Dict]]:
        """生成测试场景"""
        logger.info("🤖 开始生成渗透测试场景")
        
        try:
            # 生成提示词
            prompt = self.prompt_generator.generate_scenario_prompt(target_info)
            
            # 调用QwQ-32B模型
            response = self.qwq_client.generate_scenarios(prompt)
            
            if not response:
                logger.error("❌ 场景生成失败")
                return None
            
            # 解析响应
            if 'scenarios' in response:
                scenarios = response['scenarios']
                logger.info(f"✅ 成功生成 {len(scenarios)} 个测试场景")
                return scenarios
            elif 'raw_response' in response:
                logger.warning("⚠️ 收到原始响应，尝试手动解析")
                # 这里可以添加更复杂的解析逻辑
                return self._parse_raw_response(response['raw_response'])
            else:
                logger.error("❌ 无法解析模型响应")
                return None
                
        except Exception as e:
            logger.error(f"❌ 场景生成异常: {e}")
            return None
    
    def _parse_raw_response(self, raw_response: str) -> List[Dict]:
        """解析原始响应"""
        # 简单的备用场景
        return [
            {
                "id": "basic_sql_injection",
                "name": "基础SQL注入测试",
                "category": "A03_Injection",
                "severity": "high",
                "description": "测试基础的SQL注入漏洞",
                "target_endpoints": ["/"],
                "test_steps": [
                    {
                        "step": 1,
                        "action": "GET request with SQL injection payload",
                        "payload": "id=1' OR '1'='1",
                        "expected_response": "Database error or unexpected behavior"
                    }
                ],
                "success_indicators": ["mysql_fetch_array", "syntax error"],
                "risk_assessment": "High risk of data breach",
                "mitigation": "Use parameterized queries"
            }
        ]
    
    def execute_penetration_test(self, target_url: str, scenarios: List[Dict]) -> Dict:
        """执行渗透测试"""
        logger.info(f"🎯 开始执行渗透测试: {len(scenarios)} 个场景")
        
        self.web_scanner.stats['start_time'] = time.time()
        
        results = {
            'target_url': target_url,
            'start_time': time.time(),
            'results': [],
            'statistics': {}
        }
        
        try:
            # 执行每个测试场景
            for i, scenario in enumerate(scenarios, 1):
                logger.log_scan_progress(i, len(scenarios), target_url)
                
                # 为每个端点执行场景
                target_endpoints = scenario.get('target_endpoints', [target_url])
                for endpoint in target_endpoints:
                    if endpoint.startswith('/'):
                        full_url = target_url.rstrip('/') + endpoint
                    else:
                        full_url = endpoint
                    
                    result = self.web_scanner.execute_scenario(scenario, full_url)
                    results['results'].append(result)
                
                # 添加延迟以避免过于频繁的请求
                if self.config.get('security', {}).get('rate_limiting', True):
                    time.sleep(1 / self.config.get('security', {}).get('requests_per_second', 5))
            
            self.web_scanner.stats['end_time'] = time.time()
            results['end_time'] = time.time()
            results['scan_duration'] = results['end_time'] - results['start_time']
            results['statistics'] = self.web_scanner.get_scan_statistics()
            
            logger.info(f"✅ 渗透测试执行完成，耗时 {results['scan_duration']:.2f} 秒")
            return results
            
        except Exception as e:
            logger.error(f"❌ 渗透测试执行异常: {e}")
            results['error'] = str(e)
            return results
    
    def run_full_test(self, target_url: str) -> bool:
        """运行完整的渗透测试流程"""
        logger.info("🚀 开始完整的渗透测试流程")
        
        try:
            # 1. 验证目标
            if not self.validate_target(target_url):
                return False
            
            # 2. 测试API连接
            if not self.qwq_client.test_connection():
                logger.error("❌ QwQ-32B API连接失败")
                return False
            
            # 3. 收集目标信息
            target_info = self.gather_target_info(target_url)
            
            # 4. 生成测试场景
            scenarios = self.generate_test_scenarios(target_info)
            if not scenarios:
                logger.error("❌ 无法生成测试场景")
                return False
            
            # 5. 执行渗透测试
            scan_results = self.execute_penetration_test(target_url, scenarios)
            
            # 6. 生成报告
            report_files = self.report_generator.generate_report(scan_results, target_info)
            
            # 7. 输出结果摘要
            self._print_summary(scan_results, report_files)
            
            logger.info("🎉 渗透测试流程完成")
            return True
            
        except Exception as e:
            logger.error(f"💥 渗透测试流程异常: {e}")
            return False
    
    def _print_summary(self, scan_results: Dict, report_files: Dict):
        """打印测试结果摘要"""
        print("\n" + "="*60)
        print("渗透测试结果摘要")
        print("="*60)
        
        stats = scan_results.get('statistics', {})
        print(f"目标URL: {scan_results.get('target_url')}")
        print(f"扫描耗时: {scan_results.get('scan_duration', 0):.2f} 秒")
        print(f"总请求数: {stats.get('total_requests', 0)}")
        print(f"成功请求: {stats.get('successful_requests', 0)}")
        print(f"发现漏洞: {stats.get('vulnerabilities_found', 0)}")
        print(f"成功率: {stats.get('success_rate', 0):.1f}%")
        
        print("\n生成的报告文件:")
        for format_type, file_path in report_files.items():
            print(f"  {format_type.upper()}: {file_path}")
        
        print("\n" + "="*60)


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='Web渗透测试工具 - 基于QwQ-32B模型')
    parser.add_argument('target', help='目标URL (例如: https://example.com)')
    parser.add_argument('--config', default='config/config.yaml', help='配置文件路径')
    parser.add_argument('--test-api', action='store_true', help='仅测试API连接')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    try:
        # 创建工具实例
        tool = WebPenTestTool(args.config)
        
        if args.test_api:
            # 仅测试API连接
            if tool.qwq_client.test_connection():
                print("✅ API连接测试成功")
                return 0
            else:
                print("❌ API连接测试失败")
                return 1
        
        # 运行完整测试
        success = tool.run_full_test(args.target)
        return 0 if success else 1
        
    except KeyboardInterrupt:
        logger.info("⏹️ 用户中断测试")
        return 1
    except Exception as e:
        logger.error(f"💥 程序异常: {e}")
        return 1


if __name__ == "__main__":
    sys.exit(main())
