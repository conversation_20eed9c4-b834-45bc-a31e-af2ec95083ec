# Web渗透测试工具

基于QwQ-32B模型的智能Web渗透测试工具，能够自动生成测试场景并执行安全测试。

## 🚀 特性

- **智能场景生成**: 使用QwQ-32B模型生成针对性的渗透测试场景
- **自动化执行**: 自动执行生成的测试场景并分析结果
- **全面覆盖**: 基于OWASP Top 10的安全测试覆盖
- **多格式报告**: 支持JSON、HTML、TXT格式的测试报告
- **全局日志**: 详细的日志记录和进度跟踪
- **安全设计**: 内置安全限制和速率控制

## 📋 系统要求

- Python 3.8+
- 网络连接（用于API调用）
- QwQ-32B API访问权限

## 🛠️ 安装

### 1. 克隆项目
```bash
git clone <repository-url>
cd ai_safety_testing
```

### 2. 运行安装脚本
```bash
python setup.py
```

### 3. 配置API密钥
编辑 `.env` 文件，设置您的API密钥：
```bash
QWQ_API_KEY=your_api_key_here
QWQ_API_ENDPOINT=https://api.groq.com/openai/v1/chat/completions
```

### 4. 测试安装
```bash
python main.py --test-api
```

## 🎯 使用方法

### 基本用法
```bash
python main.py https://example.com
```

### 高级选项
```bash
# 使用自定义配置文件
python main.py https://example.com --config custom_config.yaml

# 详细输出模式
python main.py https://example.com --verbose

# 仅测试API连接
python main.py --test-api
```

## 📁 项目结构

```
ai_safety_testing/
├── main.py                 # 主程序入口
├── setup.py                # 安装脚本
├── requirements.txt        # 依赖包列表
├── README.md              # 项目文档
├── .env.example           # 环境变量示例
├── config/
│   └── config.yaml        # 主配置文件
├── modules/
│   ├── __init__.py
│   ├── logger.py          # 日志系统
│   ├── qwq_client.py      # QwQ-32B客户端
│   ├── web_scanner.py     # Web扫描器
│   └── report_generator.py # 报告生成器
├── prompts/
│   └── pentest_prompts.py # 提示词工程
├── logs/                  # 日志文件目录
└── reports/              # 测试报告目录
```

## ⚙️ 配置说明

### 主配置文件 (config/config.yaml)

```yaml
# QwQ-32B模型配置
model:
  name: "qwq-32b"
  api_endpoint: "https://api.groq.com/openai/v1/chat/completions"
  max_tokens: 4096
  temperature: 0.7
  timeout: 60

# 扫描配置
scanning:
  max_concurrent_requests: 10
  request_timeout: 30
  user_agent: "WebPenTestTool/1.0"
  verify_ssl: false
  max_retries: 3

# 安全配置
security:
  rate_limiting: true
  requests_per_second: 5
  exclude_patterns:
    - "logout"
    - "delete"
    - "admin/users/delete"
```

## 🔍 测试场景类型

工具支持以下类型的安全测试：

1. **注入攻击**
   - SQL注入（联合查询、布尔盲注、时间盲注）
   - XSS（反射型、存储型、DOM型）
   - 命令注入
   - LDAP注入

2. **身份验证和会话管理**
   - 弱密码检测
   - 会话固定
   - 会话劫持
   - 权限提升

3. **访问控制**
   - 垂直权限提升
   - 水平权限提升
   - 直接对象引用
   - 强制浏览

4. **敏感数据暴露**
   - 信息泄露
   - 错误信息暴露
   - 敏感文件访问

5. **安全配置错误**
   - 默认配置检测
   - 目录遍历
   - HTTP安全头检查

## 📊 报告格式

### JSON报告
包含完整的测试数据和结构化结果，适合程序化处理。

### HTML报告
可视化的测试报告，包含图表和详细分析，适合人工审阅。

### 文本报告
简洁的文本格式报告，适合快速查看和命令行处理。

## 🛡️ 安全考虑

1. **负责任的测试**: 仅对您拥有或有明确授权的系统进行测试
2. **速率限制**: 内置请求频率控制，避免对目标系统造成影响
3. **排除模式**: 支持配置排除特定的危险操作
4. **SSL验证**: 可配置的SSL证书验证
5. **日志记录**: 完整的操作日志，便于审计

## 🔧 故障排除

### 常见问题

1. **API连接失败**
   - 检查API密钥是否正确设置
   - 验证网络连接
   - 确认API端点URL正确

2. **依赖包安装失败**
   - 升级pip: `python -m pip install --upgrade pip`
   - 使用国内镜像: `pip install -r requirements.txt -i https://pypi.tuna.tsinghua.edu.cn/simple/`

3. **权限错误**
   - 确保有写入logs和reports目录的权限
   - 在Linux/Mac上可能需要使用sudo

### 日志查看
```bash
# 查看最新日志
tail -f logs/pentest_$(date +%Y%m%d).log

# 查看错误日志
grep "ERROR" logs/pentest_*.log
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进这个工具。

## 📄 许可证

本项目采用MIT许可证。

## ⚠️ 免责声明

本工具仅用于授权的安全测试。使用者需要确保：
1. 仅对自己拥有或有明确授权的系统进行测试
2. 遵守当地法律法规
3. 负责任地使用测试结果

作者不对工具的误用或滥用承担任何责任。

## 📞 支持

如有问题或建议，请通过以下方式联系：
- 提交GitHub Issue
- 发送邮件至项目维护者

---

**注意**: 这是一个安全测试工具，请负责任地使用！
