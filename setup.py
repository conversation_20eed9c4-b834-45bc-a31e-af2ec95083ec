#!/usr/bin/env python3
"""
Web渗透测试工具安装脚本
"""

import os
import sys
import subprocess
from pathlib import Path


def check_python_version():
    """检查Python版本"""
    if sys.version_info < (3, 8):
        print("❌ 需要Python 3.8或更高版本")
        sys.exit(1)
    print(f"✅ Python版本检查通过: {sys.version}")


def install_dependencies():
    """安装依赖包"""
    print("📦 开始安装依赖包...")
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ 依赖包安装完成")
    except subprocess.CalledProcessError as e:
        print(f"❌ 依赖包安装失败: {e}")
        sys.exit(1)


def create_directories():
    """创建必要的目录"""
    directories = ["logs", "reports", "config"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"📁 创建目录: {directory}")


def setup_environment():
    """设置环境文件"""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        import shutil
        shutil.copy(env_example, env_file)
        print("📝 创建环境配置文件: .env")
        print("⚠️  请编辑 .env 文件并设置您的API密钥")
    else:
        print("ℹ️  环境配置文件已存在")


def verify_installation():
    """验证安装"""
    print("🔍 验证安装...")
    
    try:
        # 测试导入主要模块
        from modules.logger import get_logger
        from modules.qwq_client import QwQClient
        from modules.web_scanner import WebScanner
        from modules.report_generator import ReportGenerator
        
        print("✅ 模块导入测试通过")
        
        # 测试配置文件
        import yaml
        with open("config/config.yaml", 'r', encoding='utf-8') as f:
            config = yaml.safe_load(f)
        print("✅ 配置文件测试通过")
        
        print("🎉 安装验证完成")
        
    except Exception as e:
        print(f"❌ 安装验证失败: {e}")
        sys.exit(1)


def main():
    """主安装流程"""
    print("🚀 开始安装Web渗透测试工具")
    print("="*50)
    
    check_python_version()
    create_directories()
    install_dependencies()
    setup_environment()
    verify_installation()
    
    print("\n" + "="*50)
    print("✅ 安装完成！")
    print("\n下一步:")
    print("1. 编辑 .env 文件，设置您的QwQ-32B API密钥")
    print("2. 运行: python main.py --test-api 测试API连接")
    print("3. 运行: python main.py https://example.com 开始渗透测试")
    print("\n使用 python main.py --help 查看更多选项")


if __name__ == "__main__":
    main()
